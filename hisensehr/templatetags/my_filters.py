from hisensehr.constantvariables import LEAVE_VIEW_ONLY_ROLES, permission_list, sub_permission_list
from django import template
from hisensehr.helper import getMyMenu, calc_user_total_leave, calc_user_total_leave_as_whole,attendance_summary
from django.core.paginator import Paginator
import pdb
import ast
from ..models import APPROVAL_STATUS, APPROVED, GOAL, KPI, Countries, FlightTicketAttachments, LeaveStatusLog, ProbationApprovalStatus, User, PayrollMaster, PayrollDetail, PayrollDivision, PROBATION_STATUS ,ActivityLog
from hisensehr.constantvariables import KPI_PERCENTAGE, RATING_PERCENTAGE, UAE_TIMEZONE
from django.utils import timezone
from django.utils.timesince import timesince
from ..models import LeavePolicy, LeaveSetting, UserPolicyLeave, LeaveRequest, LeavePermission, CourseContent, \
    CourseContentHistory,AttendanceLog,LeaveAdjustment, KpiActionPermission, KpiUserTimePeriod, KpiUserGoal,FlightTicketAdjustment, \
    FlightTicket, KpiUserYear,KPIApprovalStatus, AppraisalApprovalStatus, AppraisalLevelPermissions

from datetime import date, datetime, timedelta
from dateutil import relativedelta
from dateutil import tz
from datetime import date
from dateutil import parser
import re
from django.db.models import Q, Count
from django.db.models import Case, CharField, Value, When
from django.db.models import Value as V
from django.db.models.functions import Concat
from django.db.models import F
from hisensehr.constantvariables import PROBATION_WAITING_EMPLOYEE_APPROVAL,PROBATION_WAITING_HR_APPROVAL,PROBATION_WAITING_MD_APPROVAL ,\
                                    PROBATION_MD_APPROVED, PROBATION_MD_APPROVED
# import filetype
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes
from django.db.models import Prefetch, Subquery
from django.db.models import Sum
from ..helper import is_admin_user,attendance_log_summary, UserTotalLeave,RemPoints, UserLeaveCalculation
from hisensehr.helper import encrypt_me
import pandas as pd
import numpy as np
try:
    import zoneinfo
except ImportError:
    from backports import zoneinfo
from django.shortcuts import get_object_or_404
from django.db.models import Max

register = template.Library()


@register.simple_tag
def permission_label(name):
    label = ''
    if name in permission_list:
        label = permission_list[name]

    return label


@register.simple_tag
def has_menu_access(group_access, menu_id):
    for group_acces in group_access:
        if group_acces.dynamic_menu_id == menu_id:
            return True
    return False


@register.simple_tag
def get_action_menus(permissions, content_type_id):
    action_perms = []
    for permission in permissions:
        if str(permission.content_type_id) == str(content_type_id):
            action_perms.append(permission)
    return action_perms


@register.simple_tag
def has_action_access(gp_permisions, perm_id):
    for gp_permision in gp_permisions:
        if gp_permision.id == perm_id:
            return True
    return False


@register.simple_tag
def sub_permission_label(name):
    label = ''
    if name in sub_permission_list:
        label = sub_permission_list[name]

    return label


@register.simple_tag
def get_proper_elided_page_range(p, number, on_each_side=1, on_ends=1):
    paginator = Paginator(p.object_list, p.per_page)
    return paginator.get_elided_page_range(number=number, on_each_side=1, on_ends=1)


@register.simple_tag
def get_as_item_as_list(as_list):
    res = []
    if "[" in as_list:
        res = ast.literal_eval(as_list)
    else:
        res.append(as_list)
    return res


@register.simple_tag
def get_level_employee(leave_permset, level_id):
    if leave_permset:
        for perm_set in leave_permset:
            if perm_set.leave_level_id == level_id:
                if perm_set.assigned_to:
                    return perm_set.assigned_to.first_name + " " + perm_set.assigned_to.last_name
        return "--"
    else:
        return "--"


@register.simple_tag
def get_string(value):
    return str(value)


@register.simple_tag
def encrypt_id(id):
    return encrypt_me(id)          

@register.filter(name='encrypt_value')
def encrypt_value(value):
    encrypted = urlsafe_base64_encode(force_bytes("{0}***{1}".format(value, 'jesus')))
    return str(encrypted)

@register.simple_tag
def get_point_rate_comment(rate, point_comments):
    comment = ''
    for point_comment in point_comments:
        if point_comment.probation_rate_id == rate:
            comment = point_comment.description
            break
    return comment

@register.simple_tag
def get_point_rate(probation_form, point, rate):
    point_rate_data = {}
    if probation_form:
        user_probation_evals = probation_form.user_probation_evals.all()   
        for user_probation_eval in user_probation_evals:
            if user_probation_eval.probation_rate_id == rate and user_probation_eval.probation_point_id == point:
                point_rate_data = {'found':True,'comment':user_probation_eval.probation_comment}
                break
    return point_rate_data


@register.simple_tag
def get_months_completed(joining_date):
    diff_months = None
    today = date.today()
    diff_months = (relativedelta.relativedelta(today, joining_date))
    diff_months = diff_months.months + (diff_months.years * 12)
    if diff_months >= 12:
        # Convert to years
        diff_years = diff_months // 12 
        if diff_years > 1 :
            return f"{diff_years} years"
        else : 
            return f"{diff_years} year"
    else:
        if diff_months > 1 :
            return f"{diff_months} months"
        else : 
            return f"{diff_months} month"

@register.simple_tag
def is_point_selected(probation_form, point):
    point_data = None
    if probation_form:
        user_probation_evals = probation_form.user_probation_evals.all()   
        for user_probation_eval in user_probation_evals:
            if user_probation_eval.probation_point_id == point:
                point_data = {'found':True,'rate_id':user_probation_eval.probation_rate_id,'rate':user_probation_eval.probation_rate.name,'comment':user_probation_eval.probation_comment}
                break
    return point_data

@register.simple_tag
def get_probation_status(probation_forms):
    status_data = None
    if probation_forms:
        for probation_form in probation_forms:
           try : 
            probation_form.probation_status
            status_data = {'status':probation_form.probation_status,'status_display':dict(APPROVAL_STATUS)[probation_form.probation_status]}
           except : 
            status_data = None       
    return status_data

@register.simple_tag
def get_probation_supervisor(probation_form):
    supervisor = None
    if probation_form:
        for probation_status in probation_form.probation_form_statuses.all():
            if probation_status.status == PROBATION_WAITING_EMPLOYEE_APPROVAL:
                supervisor = f"{probation_status.user.first_name} {probation_status.user.last_name}"
    return supervisor

@register.simple_tag
def get_probation_hr(probation_form):
    supervisor = None
    if probation_form:
        for probation_status in probation_form.probation_form_statuses.all():
            if probation_status.status == PROBATION_WAITING_MD_APPROVAL:
                supervisor = f"{probation_status.user.first_name} {probation_status.user.last_name}"
    return supervisor

@register.simple_tag
def get_probation_md(probation_form):
    supervisor = None
    if probation_form:
        for probation_status in probation_form.probation_form_statuses.all():
            if probation_status.status == PROBATION_MD_APPROVED:
                supervisor = f"{probation_status.user.first_name} {probation_status.user.last_name}"
    return supervisor
"""@register.simple_tag
def encrypt_me(notification):
    encrypted = ''
    if 'type' in notification.info and 'end_user' in notification.info:
        if notification.info['type'] in ['appraisal_form_self_evaluation', 'appraisal_form_hr_evaluation',
                                         'appraisal_form_super_evaluation',
                                         'appraisal_form_self_approval', 'appraisal_form_hod_permission',
                                         'appraisal_user_permission'
            , 'kpi_goal_actual_or_target_permission', 'kpi_goal_permission_kpi', 'kpi_yearly_goal_created'
            , 'kpi_yearly_goal_emp_approved', 'kpi_emp_approved']:
            encrypted = urlsafe_base64_encode(force_bytes("{0}***{1}".format(notification.info['end_user'], 'jesus')))
    # print("---encrypted----",encrypted)
    return str(encrypted)"""


@register.simple_tag
def get_level_employee_id(leave_permset, level_id):
    if leave_permset:
        for perm_set in leave_permset:
            if perm_set.leave_level_id == level_id:
                if perm_set.assigned_to:
                    return perm_set.assigned_to_id
        return None
    else:
        return None


@register.simple_tag
def get_leave_type_value(leave_typelist, type_id):
    if leave_typelist:
        for type in leave_typelist:
            if type.leave_type_id == type_id:
                if type.no_of_days:
                    return type.no_of_days
        return None
    else:
        return None


@register.simple_tag
def get_user_corr_leave_type_data(leave_type_data, type_id):
    if leave_type_data:
        for type in leave_type_data:
            if type.leave_type_id == type_id:
                return type
        return None
    else:
        return None


@register.simple_tag
def get_grade_tootip(super_grade, hr_grade):
    return 'Direct Leader Grade : {},  HR Grade : {}'.format(super_grade, hr_grade)


@register.simple_tag
def get_point_rating(point_evaluations, action):
    # print("------evaluated_points-----",point_evaluations)
    added_rating = 0
    if point_evaluations:
        for evaluated_point in point_evaluations:
            if action == 'self':
                if evaluated_point.kpi_action_id == None:
                    added_rating = evaluated_point.rating
            else:
                if evaluated_point.kpi_action_id == action:
                    added_rating = evaluated_point.rating
    return added_rating


@register.simple_tag
def get_rate_total(appraisal_consolidated, action_id):
    total = 0
    # (appraisal_consolidated)
    for consolidated in appraisal_consolidated:
        if consolidated['kpi_action'] == action_id:
            total = consolidated['score']

    return total


@register.simple_tag
def get_rated_average(rate_data, actions):
    # print("-----rate_data-----",rate_data,"-----rate_data-----",actions)
    total = 0
    for rate in rate_data:
        if rate['kpi_action'] == 10:  # comment this for multiple level
            total += rate['score']
    """if total:
        total = total/(len(actions))
        total = (total*RATING_PERCENTAGE)/100"""
    return str(round(total, 2))


@register.simple_tag
def get_total_apraisal_value(weighted_annual_score, average_rate):
    # print(weighted_annual_score, average_rate)
    total = 0
    if weighted_annual_score and average_rate:
        total = float(weighted_annual_score) + float(average_rate)

    return str(round(total, 2))


@register.simple_tag
def get_appraisal_grade(appraisal_grades, total_apraisal_value):
    # print("------evaluated_points-----",point_evaluations)

    appraisal_grade_name = ''
    if total_apraisal_value:
        total_apraisal_value = float(total_apraisal_value)

        if appraisal_grades:
            for appraisal_grade in appraisal_grades:
                if total_apraisal_value >= appraisal_grade.start and total_apraisal_value <= appraisal_grade.end:
                    appraisal_grade_name = appraisal_grade.grade
                    break
    return appraisal_grade_name


@register.filter
def get_item(dictionary, key):
    # key = str(key)
    value = ''
    if dictionary and dictionary.get(key):
        value = str(round(dictionary.get(key), 2))
    return value


@register.simple_tag
def get_annual_score(dictionary):
    # print("---dictionary---",len(dictionary))
    total = 0
    if dictionary:
        for key, value in dictionary.items():
            total += value
    if total:
        total = total / len(dictionary)

    return str(round(total, 2))


@register.simple_tag
def get_weighted_annual_score(dictionary):
    total = 0
    if dictionary:
        for key, value in dictionary.items():
            total += value

    if total:
        total = total / len(dictionary)
        total = (total * KPI_PERCENTAGE) / 100
    return str(round(total, 2))


@register.simple_tag
def get_achieved_total_percentage(user_goals_query_set):
    total = 0
    for user_goal in user_goals_query_set:
        achieved = 0
        score = 0
        actual = user_goal.user_actual.first().actual
        target = user_goal.user_target.first().target
        max_rate = user_goal.max_finish_rate
        weight = user_goal.weight
        calculation = user_goal.kpi_goal.measurement_type.calculation
        try:
            if target and actual:
                #print("--total--calculation---", calculation)
                if calculation == None or not calculation:
                    achieved = (actual / target) * 100
                else:
                    achieved = eval(calculation)
                if achieved >= max_rate:
                    achieved = max_rate
                score = (achieved * weight) / 100
        except ZeroDivisionError:
            achieved = 0

        total = total + score
    return total


@register.simple_tag
def get_achieved_percentage(actual, target, max_rate, calculation=None):
    achieved = 0
    try:
        if target and actual:
            #print("---achieved_percentage-calculation---", calculation)
            if calculation == None or not calculation:
                achieved = (actual / target) * 100
            else:
                achieved = eval(calculation)
            if achieved >= max_rate:
                achieved = max_rate
        else:
            if actual == 0 and target == 0:
                achieved = 100
                if achieved >= max_rate:
                    achieved = max_rate
    except ZeroDivisionError:
        achieved = 0
    return achieved


@register.simple_tag
def get_weightage_score(actual, target, weight, max_rate, calculation=None):
    score = 0
    achieved= 0
    try:
        if weight =='':
            weight = 0
        if actual == 0 and target == 0:
            achieved = 100
        elif actual and target:
            # print("---weightage_score-calculation---",calculation)
            if calculation == None or not calculation:
                achieved = (actual / target) * 100
            else:
                achieved = eval(calculation)
            if achieved >= max_rate:
                achieved = max_rate
        score = (achieved * weight) / 100
    except ZeroDivisionError:
        score = 0
    return score


@register.simple_tag
def get_status_color(status):
    status_dict = {
        1: "table-bg-red",
        2: "table-bg-yellow",
        3: "table-bg-violet",
        4: "table-bg-pink",
        5: "table-bg-green"
    }
    if status in status_dict.keys():
        return status_dict[status]
    else:
        return status_dict[1]


@register.simple_tag(takes_context=True)
def get_approval_status(context, perms, leave_id):
    
    request = context['request']
    approve_by = perms.assigned_to
    # Check user approved the request
    data = {}
    status = None
    try:
        leave_log = LeaveStatusLog.objects.filter(leave_request_id=leave_id, added_by=perms.assigned_to)
        if leave_log:
            if leave_log[0].is_approve:
                status = 'approved'
            else:
                status = 'rejected'
            data['day_n_date'] = f"{leave_log[0].updated_at.strftime('%a')}, {leave_log[0].updated_at.strftime('%-d %b %Y')}"
            from_zone, to_zone, utc = tz.tzutc(), tz.gettz('Asia/Dubai'), leave_log[0].updated_at
            central = utc.astimezone(to_zone)
            data['time'] = central.strftime('%-I:%M %p')
            data['status'] = status
            if leave_log[0].remark:
                data['remark'] = leave_log[0].remark
        else:
            status = None
            data['status'] = status
    except Exception as dberror:
        data['status'] = status
    return data

@register.simple_tag
def get_approval_status_updated(leave_record, leave_levels, leave_status):
    result, dict_data, rem_items,no_level = [], {}, [], []
    last_log_entry_time = None
    leave_status_log =  LeaveStatusLog.objects.filter(leave_request=leave_record).order_by('leave_level')
    if leave_status_log:
        for leave_status_log_entry in leave_status_log:
            dict_data['name'] = leave_status_log_entry.added_by.first_name+ " " + leave_status_log_entry.added_by.last_name
            dict_data['profile_image'] = leave_status_log_entry.added_by.profile.profile_pic.url \
                                            if leave_status_log_entry.added_by.profile.profile_pic else ''
            dict_data['designation'] = leave_status_log_entry.added_by.profile.designation.name \
                                        if leave_status_log_entry.added_by.profile.designation else None  
            
            from_zone, to_zone, utc = tz.tzutc(), tz.gettz('Asia/Dubai'), leave_status_log_entry.updated_at
            central = utc.astimezone(to_zone)
            
            if leave_status_log_entry.is_approve == True:
                dict_data['status'] = 'complete'
                dict_data['day_n_date'] = f"{leave_status_log_entry.updated_at.strftime('%a')}, \
                                            {leave_status_log_entry.updated_at.strftime('%-d %b %Y')}"
                dict_data['time'] = central.strftime('%-I:%M %p')
            elif leave_status_log_entry.is_approve == False:
                dict_data['status'] = 'reject'
                dict_data['day_n_date'] = f"{leave_status_log_entry.updated_at.strftime('%a')}, \
                                            {leave_status_log_entry.updated_at.strftime('%-d %b %Y')}"
                dict_data['time'] = central.strftime('%-I:%M %p')
            else:
                dict_data['status'] = 'pending'
            if leave_status_log_entry.leave_level:
                dict_data['level'] = leave_status_log_entry.leave_level.id
            if leave_status_log_entry.remark:
                dict_data['remark'] = leave_status_log_entry.remark
            
            
            result.append(dict_data)
            dict_data = {}
    return result
                
    


@register.simple_tag
def display_user_group(user):
    user = User.objects.get(id=user)
    user_group = user.groups.values_list('name', flat=True)
    if not user_group:
        if user.is_superuser:
            user_group = "Super Admin"
        else:
            user_group = "No Role Assigned"
    else:
        user_group = user_group[0]
    return user_group


@register.simple_tag
def get_stage_names(approved_kpis, user_id):
    stages = ''
    for approved_kpi in approved_kpis:
        if approved_kpi.kpi_user_year.user_id == user_id:
            stages = stages + approved_kpi.kpi_time_period.name + " ,"
    return stages


@register.simple_tag
def get_appraisal_statuses(appraisals, kpi_year):
    found = False
    appraisal_status = 0
    appraisal_id = 0
    if appraisals and kpi_year:
        for appraisal in appraisals:
            if appraisal.start_date <= kpi_year.start_date <= kpi_year.end_date:
                found = True
            if appraisal.end_date <= kpi_year.start_date <= kpi_year.end_date:
                found = True
            if found:
                appraisal_status = appraisal.status
                appraisal_id = appraisal.id
            if KpiUserYear.objects.select_related('user').filter(user=appraisals.last().user).last().appraisal_status == 4:
                appraisal_status = 'rejected'
                appraisal_id = appraisal.id

    return {'appraisal_status': appraisal_status, 'appraisal_id': appraisal_id}


@register.simple_tag
def get_reviewed_by(appraisals, kpi_year):
    found = False
    reviewed_by = []
    if appraisals and kpi_year:
        for appraisal in appraisals:
            if appraisal.start_date <= kpi_year.start_date <= kpi_year.end_date:
                found = True
            if appraisal.end_date <= kpi_year.start_date <= kpi_year.end_date:
                found = True

            if found:
                statuses = appraisal.appraisal_form_statuses.all()
                for status in statuses:
                    reviewed_by.append(status)

    return reviewed_by


#

@register.simple_tag
def get_leave_approval_status(leave_record, leave_level):
    status = False
    if LeaveStatusLog.objects.filter(leave_request=leave_record, leave_level_id=leave_level.leave_level_id,
                                     is_approve=True).exists():
        status = True
    return status


@register.simple_tag
def make_as_list(string):
    if string:
        values = string.split(',')
        res = [eval(i) for i in values]
    return res


@register.filter()
def time_until(value):
    now = timezone.now()
    local_timezone = zoneinfo.ZoneInfo(UAE_TIMEZONE)
    created_at = value.astimezone(local_timezone)
    if value:
        if value.strftime("%Y-%m-%d") == now.strftime("%Y-%m-%d"):
            difference = now - value
            hours = difference.days * 24 + difference.seconds // 3600
            minutes = (difference.seconds % 3600) // 60
            if difference.days == 0 and hours == 1:
                return "{0} hour ago".format(hours)
            elif difference.days == 0 and hours >= 1:
                return "{0} hours ago".format(hours)
            elif difference.days == 0 and hours < 1 and minutes == 1:
                return "{0} minute ago".format(minutes)
            elif difference.days == 0 and hours < 1 and minutes != 0:
                return "{0} minutes ago".format(minutes)
            elif difference.days == 0 and hours == 0 and minutes == 0:
                return "Just now"
            else:
                return created_at.strftime("%d %B %Y %I %p")
        else:
            return created_at.strftime("%d %B %Y %I %p")


@register.simple_tag
def get_day_name(date, char):
    day_name = None
    if char == 'short':
        if date:
            day_name = date.strftime('%a')
    elif char == 'full':
        day_name = date.strftime('%A')
    return day_name


@register.filter()
def to_int(value):
    return int(value)


@register.filter()
def to_string(value):
    return str(value)


@register.simple_tag
def get_leave_type_count(leave_type_id):
    count = 0
    if leave_type_id:
        try:
            policy = LeavePolicy.objects.filter(is_active=True).first().id
            leave_setting = LeaveSetting.objects.filter(leave_policy_id=policy, leave_type_id=leave_type_id).first()
            count = leave_setting.no_of_days
        except Exception as dberror:
            print(dberror)
    return count


@register.simple_tag
def get_user_remainin_leave(user, leave_type_id):
    available_leaves, filt_condition = 0, {}
    if not user.is_superuser:
        if leave_type_id != 2:
            joining_date = user.profile.date_of_joining
            today = date.today()
            diff_months = (relativedelta.relativedelta(today, joining_date))
            diff_months = diff_months.months + (diff_months.years * 12)
            leave_policy = LeavePolicy.objects.get(id=user.profile.leave_policy_id)
            if leave_type_id != 1 and leave_type_id != 2:
                filt_condition['year'] = today.year
            user_leave = UserPolicyLeave.objects.filter(user=user, leave_type_id=leave_type_id,
                                                        leave_policy=leave_policy, **filt_condition)
            if user_leave:
                leaves_taken = user_leave[0].leaves_taken
            else:
                leaves_taken = 0
            func_response = calc_user_total_leave(user, str(leave_type_id), leaves_taken, leave_policy, diff_months)
            if func_response['status']:
                available_leaves = func_response['available_leaves']
        else:
            available_leaves = '--'
    return available_leaves


@register.simple_tag
def get_user_total_leave(user, leave_type_id):
    available_leaves = 0
    if not user.is_superuser:
        joining_date = user.profile.date_of_joining
        today = date.today()
        diff_months = (relativedelta.relativedelta(today, joining_date))
        diff_months = diff_months.months + (diff_months.years * 12)
        leave_policy = LeavePolicy.objects.get(id=user.profile.leave_policy_id)
        func_response = calc_user_total_leave_as_whole(user, str(leave_type_id), leave_policy, diff_months)
        if func_response['status']:
            available_leaves = func_response['available_leaves']
            if available_leaves != 0:
                a = str(available_leaves)
                valueAfterPoint = a.split('.')[1]
                valueAfterPoint = int(valueAfterPoint)
                if valueAfterPoint == 0:
                    available_leaves = int(available_leaves)
    return available_leaves


@register.simple_tag
def get_leave_took_count(user, leave_type_id):
    leave_took = 0
    if not user.is_superuser:
        leave = UserPolicyLeave.objects.filter(user=user, leave_type_id=leave_type_id,
                                               leave_policy=user.profile.leave_policy)
        if leave:
            if leave_type_id != '1':
                leave_took = leave.aggregate(Sum('leaves_taken'))['leaves_taken__sum']
            else:
                leave_took = leave[0].leaves_taken
            if leave_took != 0 or leave_took == 0.0:
                a = str(leave_took)
                valueAfterPoint = a.split('.')[1]
                valueAfterPoint = int(valueAfterPoint)
                if valueAfterPoint == 0:
                    leave_took = int(leave_took)
    return leave_took


@register.filter()
def date_as_j(value):
    return_date = None
    if value:
        date = value.strftime("%Y-%m-%d")
        split_date = date.split('-')
        return_date = split_date[0] + '-' + split_date[1] + '-' + split_date[2].lstrip('0')
    return return_date


@register.simple_tag
def get_experience_year(experience):
    try:
        experience = int(experience)
        if experience < 12:
            exp_string = f"{experience} Months Exp"
        elif experience % 12 == 0:
            exp_string = f"{experience / 12} Year Exp"
        else:
            exp_string = f"{int(experience / 12)} year+ Exp"
        return exp_string
    except:
        exp_string = f"{experience} Months Exp"
    finally:
        return exp_string


@register.simple_tag
def get_about_to_expiry(date):
    try:
        expiry_status = None
        today = datetime.now().date()
        date = date.date()
        diffrence = date - today
        diffrence_days = diffrence.days
        if diffrence_days < 0:
            expiry_status = "Expired"
        elif diffrence_days <= 21:
            expiry_status = "About to expire"
    except:
        expiry_status = None
    return expiry_status


@register.simple_tag
def get_document_icon(extension):
    icons = {
        'pdf': 'pdf-icon.svg',
        'txt': 'txt_icon.svg',
        'jpeg': 'jpeg_icon.svg',
        'jpg': 'jpeg_icon.svg',
        'docx': 'doc_file_icon.svg',
        'doc': 'doc_file_icon.svg',
        'png': 'jpeg_icon.svg'
    }
    if extension in icons.keys():
        return f'/static/admin/assets/images/{icons[extension]}'
    else:
        return '/static/admin/assets/images/video-play.svg'


@register.simple_tag
def find_days_left(end_date):
    out_string = ""
    try:
        today = date.today()
        start_date = parser.parse(today.strftime("%Y-%m-%d"))
        end_date = parser.parse(end_date.strftime("%Y-%m-%d"))
        days = (end_date - start_date).days
        out_string = str(days) + " Days left"
        return out_string
    except Exception as dberror:
        return out_string


@register.simple_tag(takes_context=True)
def get_on_leave_users_today(context):
    request = context['request']
    filt = {}
    users = LeavePermission.objects.filter(assigned_to=request.user).values_list('user_id', flat=True)
    today = date.today()
    #If the user is not admin or not special level user show assigned to me today leave request count other wise today all leave request count
    if not is_admin_user(request.user) and not any(item in LEAVE_VIEW_ONLY_ROLES for item in request.user.groups.values_list('id', flat=True)):
        filt['user_id__in'] = users
    #Today Leave count
    count = LeaveRequest.objects.filter(start_date__lte=today,end_date__gte=today, status=3, **filt).count()
    # count = Profile.objects.filter(user_status_id__in=[4,9,11]).count()
    return count


@register.simple_tag
def get_user_leave_levels(user):
    leave_levels = ''
    leave_levels = LeavePermission.objects.filter(user=user, is_active=True)
    return leave_levels


@register.simple_tag
def get_leave_approval_statuses(leave_record, leave_level, user_in_charge):
	status = 'pending'
	# leave_status = LeaveStatusLog.objects.filter(leave_request=leave_record, leave_level_id=leave_level.leave_level_id)
	leave_status = LeaveStatusLog.objects.filter(leave_request=leave_record, added_by=user_in_charge)
	if leave_status and leave_status[0].is_approve:
		status = 'complete'
	elif leave_status and not leave_status[0].is_approve:
		status = 'reject'
	else:
		status = 'pending'
	return {'status':status}

@register.simple_tag
def make_text_to_link(text):
    regex = r"(?i)\b((?:https?://|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'\".,<>?«»“”‘’]))"
    linkList = re.findall(regex, text)
    if len(linkList) > 0:
        return True


@register.simple_tag
def posts_count(posts):
    try:
        cnt = int(posts) - 3
        return cnt
    except:
        return 1


@register.filter
def st_nd_rd_th(value):
    if value <= 3:
        response = "1st" if int(value) == 1 else "2nd" if int(value) == 2 else "3rd"
    else:
        response = f"{value}th"
    return response


@register.simple_tag
def get_course_documents_count(course_id):
    choices = dict(CourseContent.file_type.field.choices)
    whens = [When(file_type=k, then=Value(v)) for k, v in choices.items()]
    data = CourseContent.objects.values('file_type').annotate(
        course_type=Case(*whens, output_field=CharField())).values('course_type').annotate(count=Count('id')).filter(
        course_id=course_id).order_by('file_type')
    return data


@register.simple_tag
def responsibilities(responsibilities):
    # s=[]
    # p = responsibilities.split("</li>")
    # for i in p:
    # 	print("kkkk")
    # pdb.set_trace()
    # return responsibilities.strip('][').split(', ')
    return ['fffffffffff', 'fbbbbbbbbbbb']


@register.simple_tag
def get_leave_direct_approvals(leave_request):
	leave_levels = LeavePermission.objects.filter(user=leave_request.user).values_list('assigned_to_id', flat=True)
	leave_approved_hrs = LeaveStatusLog.objects.\
		annotate(name=Concat('added_by__first_name', V(' '), 'added_by__last_name')).\
		annotate(profile_image=F('added_by__profile__profile_pic')).\
		filter(leave_request=leave_request).exclude(added_by__in=leave_levels).only('added_by', 'updated_at')
	return leave_approved_hrs

@register.simple_tag
def ticket_status(status):
    status_dict = {
        '1': "table-bg-yellow",
        '2': "table-bg-violet",
        '3': "table-bg-green",
        '4': "table-bg-pink",
    }
    if str(status) in status_dict:
        return status_dict[str(status)]


@register.filter()
def converto_utctime(value):
    from_zone, to_zone, utc = tz.tzutc(), tz.gettz('Asia/Dubai'), value
    central = utc.astimezone(to_zone)
    time = central.strftime('%-I:%M %p')
    return time


@register.simple_tag
def get_content_percentage(content_id, user_id, duration, status):
    percentage = 0
    content = CourseContentHistory.objects.filter(course_content_id=content_id, user_id=user_id)
    status = content[0].status if content else ''
    if duration:
        if content:
            completed_duration = content.first().completed_duration
            if status == 3:
                percentage = 100
            else:
                if completed_duration:
                    percentage = int((completed_duration / duration) * 100)
                    percentage = 100 if percentage > 100 else percentage
    else:
        percentage = 100 if status == 3 else 0
    return percentage

# ----------
@register.simple_tag
def get_watch_percentage(duration, history):
    percentage = 0
    if history:
        history = history[0]
        percentage = 100 if history.status == 3 else int((history.completed_duration / duration) * 100)
    return percentage


from django.utils.safestring import mark_safe


@register.simple_tag
def payroll_years():
    # print(category_id,'category_id1')

    options_list = ''
    selected = ''
    current_year = date.today().year
    prev = current_year - 1
    options_list += '<option value="' + str(current_year) + '" ' + selected + '>' + str(current_year) + '</option>'
    options_list += '<option value="' + str(prev) + '" ' + selected + '>' + str(prev) + '</option>'

    return mark_safe(options_list)


@register.simple_tag
def payroll_years_and_month():
    options_list = ''
    selected = ''
    current_year = date.today().year
    prev = current_year - 1
    month_list = list(calendar.month_name)
    month_list.pop(0)
    current_month = date.today().month
    current_month_list = month_list[:current_month]
    for month_name in month_list:
        options_list += '<option value="' + str(prev) + '" ' + selected + '>' + month_name + ' ' + str(
            prev) + '</option>'

    for month_name in current_month_list:
        options_list += '<option value="' + str(current_year) + '" ' + selected + '>' + month_name + ' ' + str(
            current_year) + '</option>'

    return mark_safe(options_list)


import calendar


@register.simple_tag
def payroll_months():
    options_list = ''
    selected = ''
    month_list = list(calendar.month_name)
    month_list.pop(0)
    for month_name in month_list:
        options_list += '<option value="' + month_name + '" ' + selected + '>' + month_name + '</option>'
    return mark_safe(options_list)


@register.simple_tag
def payroll_employees():
    options_list = ''
    selected = ''
    employees = User.objects.filter(is_active=True, is_superuser=False)
    for employee in employees:
        options_list += '<option value="' + str(employee.id) + '" ' + selected + '>' + employee.email + '</option>'
    return mark_safe(options_list)


@register.simple_tag
def get_course_percentage(user, course):
    percentage = "0%"
    contents_duration = \
        CourseContent.objects.select_related('course').filter(course_id=course, is_active=True).aggregate(
            Sum('duration'))[
            'duration__sum']
    watch_duration = CourseContentHistory.objects. \
        select_related('course_content', 'course_content__course'). \
        filter(user=user, course_content__course_id=course).aggregate(Sum('completed_duration'))[
        'completed_duration__sum']
    if watch_duration:
        percentage = str(int((watch_duration / contents_duration) * 100)) + "%"
    return percentage

# --------- Course Percentage ------------------ #

@register.simple_tag
def get_watch_history_percentage(user, course_contents):
    percentage, completed_duration, contents_duration = "0%", 0 , 0
    for content in course_contents:
        contents_duration += content.duration
        for history in content.related_course_history.all():
            if history.user_id == user.id:
                completed_duration += history.completed_duration
    percentage = str(int((completed_duration / contents_duration) * 100)) + "%"
    return percentage


@register.filter()
def remPoints(value):
    if value != '--':
        if len(str(value)) > 1:
            if '.' in str(value):
                secondvalue = (str(value)).split('.')[1]
                if int(secondvalue) <= 0:
                    value = int(value)
    if value == None:
        value = '--'
    return value


@register.filter()
def month_name(value):
    if value:
        if isinstance(value, str):
            return calendar.month_abbr[int(value)]
        else:
            return calendar.month_abbr[value]
    else:
        return ' '


@register.simple_tag
def get_gross_pay(user_id, year):
    gross_pay = PayrollMaster.objects.filter(user_id=user_id, year=year).aggregate(Sum('gross_salary'))
    gross_pay = gross_pay['gross_salary__sum']
    return gross_pay


@register.simple_tag
def get_total_allowance(user_id, year):
    gross_pay = PayrollDetail.objects.filter(payroll_master__user_id=user_id, payroll_master__year=year,
                                             payroll_division__type=1).aggregate(Sum('amount'))
    allowance = gross_pay['amount__sum']
    return allowance


@register.simple_tag
def get_total_bonus(user_id, year):
    gross_pay = PayrollDetail.objects.filter(payroll_master__user_id=user_id, payroll_master__year=year,
                                             payroll_division__title__icontains='Yearly or Quarterly Bonus').aggregate(
        Sum('amount'))
    bonus = gross_pay['amount__sum']
    return bonus

@register.simple_tag
def get_payroll_amount_from_detail(payroll_master_id, payroll_division_id):
    try:
        record = PayrollDetail.objects.get(payroll_master_id= payroll_master_id, payroll_division_id= payroll_division_id)
        return record.amount
    except Exception as e:
        return 0

@register.simple_tag
def get_total_deduction(user_id, year):
    gross_pay = PayrollMaster.objects.filter(user_id=user_id, year=year).aggregate(Sum('deduction'))
    deduction = gross_pay['deduction__sum']
    return deduction


@register.simple_tag
def get_total_ctc(user_id, year):
    gross_pay = PayrollMaster.objects.filter(user_id=user_id, year=year).aggregate(Sum('total_salary_to_be_paid'))
    total_salary_to_be_paid = gross_pay['total_salary_to_be_paid__sum']
    return total_salary_to_be_paid


import difflib


@register.simple_tag
def get_related_column(table_columns, excel_column):
    obj = difflib.get_close_matches(excel_column, table_columns)
    if obj:
        return obj[0]
    else:
        return None


import pandas as pd


@register.simple_tag
def length_of_sheet_and_db(payroll_upload_obj):
    if payroll_upload_obj.file:
        df = pd.read_excel(payroll_upload_obj.file)
        m1 = df.shift(fill_value=0).isna().all(axis=1)

        m2 = df.notna().all(axis=1)

        df = df[(m1 & m2).cummax()]
        columns = []
        for i in df:
            columns.append(i)
        if 'Sr No.' in columns:
            columns.remove("Sr No.")
        if 'Dept' in columns:
            columns.remove("Dept")

        context = {}
        context['df'] = len(columns)
        table_columns = []
        payroll_master_list = [f.get_attname() for f in PayrollMaster._meta.fields]
        for master_column in payroll_master_list[1:-3]:
            if not '_id' in master_column:
                if '_' in master_column:
                    master_col = master_column.replace('_', ' ')
                else:
                    master_col = master_column
                table_columns.append(master_col.title())
        table_columns.insert(0, 'Employee Id')
        table_columns.remove("Month")
        table_columns.remove("Year")
        division_list = list(PayrollDivision.objects.filter(is_active=True).values_list('title', flat=True))
        for i in division_list:
            table_columns.append(i)

        if 'Sick Leaves' in table_columns:
            table_columns.remove("Sick Leaves")
        if 'Late Comings' in table_columns:
            table_columns.remove("Late Comings")
        if 'Month And Year' in table_columns:
            table_columns.remove("Month And Year")
        context['table_columns'] = len(table_columns)
        return context

@register.simple_tag
def get_prev_level_approved_status(leave_id, leave_user, user):
	approved_status = ''
	user_perm_level_id = LeavePermission.objects.filter(user_id=leave_user, assigned_to=user).values_list('leave_level_id', flat=True)
	if user_perm_level_id:
		lv_perm_st = LeavePermission.objects.filter(user_id=leave_user, leave_level_id__lt=user_perm_level_id).order_by('leave_level_id').values_list('assigned_to_id', flat=True)
		if lv_perm_st:
			status_log = LeaveStatusLog.objects.filter(leave_request_id=leave_id, added_by_id__in=lv_perm_st, is_approve=True)
			approved_status = 'approved' if lv_perm_st.count() == status_log.count() else 'pending'
	return approved_status

# ----------------------- NEW
@register.simple_tag
def get_prevlevel_approval_status(leave_perms, leave_status, current_user):
    approved_status = ''
    user_perm_level_id = None
    for perm in leave_perms:
        if perm.assigned_to_id == current_user.id:
            user_perm_level_id = perm.leave_level_id
    if user_perm_level_id:
        lv_perm_st =leave_perms.filter(leave_level_id__lt=user_perm_level_id).order_by('leave_level_id').values_list('assigned_to_id', flat=True)
        if lv_perm_st:
            status_log =leave_status.filter(added_by_id__in=lv_perm_st, is_approve=True)
            approved_status = 'approved' if lv_perm_st.count() == status_log.count() else 'pending'
    # user_perm_level_id = LeavePermission.objects.filter(user_id=leave_user, assigned_to=user).values_list('leave_level_id', flat=True)
    # if user_perm_level_id:
    # 	lv_perm_st = LeavePermission.objects.filter(user_id=leave_user, leave_level_id__lt=user_perm_level_id).order_by('leave_level_id').values_list('assigned_to_id', flat=True)
    # 	if lv_perm_st:
    # 		status_log = LeaveStatusLog.objects.filter(leave_request_id=leave_id, added_by_id__in=lv_perm_st, is_approve=True)
    # 		approved_status = 'approved' if lv_perm_st.count() == status_log.count() else 'pending'
    return approved_status


@register.simple_tag(takes_context=True)
def attendance_log(context,user,day,data,holidays,weekend,workstatuslog,profile,working_time,leave_requests):
    request = context['request']
    return attendance_log_summary(request,user,day,data,holidays,weekend,workstatuslog,profile,working_time,leave_requests,'')


@register.simple_tag
def cancel_leave_check(leave_request):
    can_cancel = False
    try:
        today = date.today()
        leave = LeaveRequest.objects.get(id=leave_request)
        if leave.start_date >= today and leave.status not in [4,5,6,7] :
            can_cancel = True
    except Exception as dberror:
        print(dberror)
    return can_cancel


@register.simple_tag
def attendance_log_years(year):
    options_list = ''
    selected = ''
    current_year = date.today().year
    if year == current_year:
        prev = current_year - 1
    else:
        prev = current_year + 1
    options_list += '<option value="' + str(prev) + '" ' + selected + '>' + str(prev) + '</option>'

    return mark_safe(options_list)


@register.simple_tag
def get_user_leave_calculation(user, leave_type_id, create=None):
    available_leaves, filt_policy, result = 0, {}, {}
    if not user.is_superuser:
        leave_policy = user.profile.leave_policy
        #1. CALCULATE AVAILABLE LEAVE
        if leave_type_id != 2:
            today = date.today()
            joining_date = user.profile.date_of_joining
            leaveadjstmnt = LeaveAdjustment.objects.filter(assigned_to_id=user.id, leave_type_id=leave_type_id).last()
            probation = user.profile.leave_policy.probation_period
            diff_months = (relativedelta.relativedelta(today, joining_date))
            diff_months = diff_months.months + (diff_months.years * 12)
            if (user.profile.hq_employee and user.profile.employement_type == 2):
                stage = 'after-probation'
            else:
                if diff_months >= probation and diff_months < 12:
                    stage = 'probation'
                elif diff_months >= 12:
                    stage = 'after-probation'
                elif diff_months < probation:
                    stage = 'before-probation'

            if leaveadjstmnt:
                filt_policy['adjusted_leave_id'] = leaveadjstmnt.id
                joining_date = leaveadjstmnt.adjusted_date
                new_diff = (relativedelta.relativedelta(today, joining_date))
                new_diff = new_diff.months + (new_diff.years * 12)
                func_response = UserTotalLeave(str(leave_type_id), leave_policy, new_diff, stage,joining_date=joining_date,today=today)
                if func_response['status']:
                    if leave_type_id == 1:
                        available_leaves = func_response['available_leaves'] + leaveadjstmnt.leave_count
                    else:
                        available_leaves = leaveadjstmnt.leave_count
            else:
                func_response = UserTotalLeave(str(leave_type_id), leave_policy, diff_months, stage, create,\
                                joining_date=joining_date,today=today)
                if func_response['status']:
                    available_leaves = func_response['available_leaves']
                if leave_type_id == 3:
                    filt_policy['year'] = date.today().year
                
            available_leaves = RemPoints(available_leaves)
        else:
            available_leaves = '--'
        
        #2. CALCULATE TAKEN LEAVE
        leavesHist = UserPolicyLeave.objects.filter(user=user, leave_type_id=leave_type_id, leave_policy=leave_policy, **filt_policy).order_by('id').last()
        taken_leaves = leavesHist.leaves_taken if leavesHist else 0
        taken_leaves = RemPoints(taken_leaves)

        #3. CALCULATE REMAINING LEAVE
        if available_leaves != '--':
            rem_leave = available_leaves - taken_leaves
        else:
            rem_leave = '--'
    
    if type(available_leaves) == float:
        available_leaves = round(available_leaves, 2)

    result['total_leave'], result['taken_leaves'], result['rem_leave'] = available_leaves, taken_leaves, rem_leave
    return result


@register.filter()
def DisplayTime(value):
    now = timezone.now()
    local_timezone = zoneinfo.ZoneInfo(UAE_TIMEZONE)
    created_at = value.astimezone(local_timezone)
    if value:
        if value.strftime("%Y-%m-%d") == now.strftime("%Y-%m-%d"):
            difference = now - value
            hours = difference.days * 24 + difference.seconds // 3600
            minutes = (difference.seconds % 3600) // 60
            if difference.days == 0 and hours == 1:
                return "{0} hour ago".format(hours)
            elif difference.days == 0 and hours >= 1:
                return "{0} hours ago".format(hours)
            elif difference.days == 0 and hours < 1 and minutes == 1:
                return "{0} minute ago".format(minutes)
            elif difference.days == 0 and hours < 1 and minutes != 0:
                return "{0} minutes ago".format(minutes)
            elif difference.days == 0 and hours == 0 and minutes == 0:
                return "Just now"
            else:
                return created_at.strftime("%H:%M %p")
        else:
            return created_at.strftime("%H:%M %p")
        
@register.filter()
def date_format_for_certificates(date):
    try:
        st = 'th'
        day = date.strftime("%d")
        st_lit = ['1','21','31','01']
        rd_list = ['3','03','23']
        nd_list = ['2','02','22']
        if day in st_lit:
            st = 'st'
        elif day in rd_list:
            st = 'rd'
        elif day in nd_list:
            st = 'nd'
        return '{0}{1} {2} {3}'.format(date.strftime("%d"),st,date.strftime("%b"),date.strftime("%Y")) 
    except Exception as e:
        pass
    
@register.filter()
def salary_formater(salary):
    if salary:
        salary_split = str(salary).split('.')
        format_salary = f"{salary:,}"
        if len(salary_split) == 2:
            format_salary = format_salary if salary_split[1] != '0' else '{0}0'.format(format_salary)
        else:
            format_salary = '{0}.00'.format(format_salary)
        return format_salary
    return ''

@register.filter()
def ticket_time_until(value):
    now = timezone.now()
    local_timezone = zoneinfo.ZoneInfo(UAE_TIMEZONE)
    created_at = value.astimezone(local_timezone)
    if value:
        if value.strftime("%Y-%m-%d") == now.strftime("%Y-%m-%d"):
            difference = now - value
            hours = difference.days * 24 + difference.seconds // 3600
            minutes = (difference.seconds % 3600) // 60
            # if difference.days == 0 and hours == 1:
            #     return "{0} hour ago".format(hours)
            # elif difference.days == 0 and hours >= 1:
            #     return "{0} hours ago".format(hours)
            # elif difference.days == 0 and hours < 1 and minutes == 1:
            #     return "{0} minute ago".format(minutes)
            # elif difference.days == 0 and hours < 1 and minutes != 0:
            #     return "{0} minutes ago".format(minutes)
            # elif difference.days == 0 and hours == 0 and minutes == 0:
            #     return "Just now"
            # else:

            return created_at.strftime("%d %B %Y, %H:%M:%S")
        else:
            return created_at.strftime("%d %B %Y, %H:%M:%S")

        
@register.simple_tag
def leaveapproval_status(leave_record, leave_levels, leave_status):
    result, dict_data, rem_items = [], {}, []
    if leave_levels:
        for levels in leave_levels:
            rem_items.append(levels.assigned_to_id)
            dict_data['name'] = levels.assigned_to.first_name+ " " + levels.assigned_to.last_name
            dict_data['profile_image'] = levels.assigned_to.profile.profile_pic.url if levels.assigned_to.profile.profile_pic else ''
            # result.append(dict_data)
            # dict_data = {}
            i = 1 
            if leave_status:
                for status in leave_status:
                    i += 1
                    if status.added_by == levels.assigned_to and status.is_approve:
                        dict_data['status'] = 'complete'
                    elif status.added_by == levels.assigned_to and not status.is_approve:
                        dict_data['status'] = 'reject'
                    elif status.added_by == levels.assigned_to:
                        dict_data['status'] = 'pending'
                    # leave_status = leave_status.exclude(added_by=status.added_by)
            else:
                dict_data['status'] = 'pending'
            result.append(dict_data)
            dict_data = {}

        leave_status = leave_status.exclude(added_by_id__in=rem_items)
        if leave_status:
            for status in leave_status:
                dict_data = {}
                dict_data['name'] = status.added_by.first_name + " " + status.added_by.last_name 
                dict_data['profile_image'] = status.added_by.profile.profile_pic.url if status.added_by.profile.profile_pic else ''
                dict_data['status'] = 'complete' if status.is_approve else 'reject'
                result.append(dict_data)
    return result


@register.simple_tag
def leaveapproval_status_new(leave_rec,leave_status):
    result, dict_data = [], {}
    leave_status = leave_status.order_by('leave_level')
    if leave_status:
        for status in leave_status:
            dict_data = {}
            dict_data['name'] = status.added_by.first_name + " " + status.added_by.last_name
            dict_data['profile_image'] = status.added_by.profile.profile_pic.url if status.added_by.profile.profile_pic else ''
            if status.is_approve:
                dict_data['status'] = 'complete'
            elif status.is_approve == False:
                dict_data['status'] = 'reject'
            else:
                dict_data['status'] = 'pending'
            result.append(dict_data)
    return result


# @register.simple_tag
# def leaveapproval_status(leave_record, leave_levels, leave_status):
#     result, dict_data, rem_items,no_level = [], {}, [], []
#     last_log_entry_time = None

#     if leave_record.status in [3,4,5,6,7] :
#         leave_status_objects  = LeaveStatusLog.objects.filter(leave_request=leave_record,is_approve__isnull=False)
        
#         if leave_status_objects.exists():
#             last_log_entry_time  = leave_status_objects.last().created_at   
#     if leave_levels:
#         for levels in leave_levels:
#             if last_log_entry_time:
#                 if levels.created_at > last_log_entry_time:
#                     continue
#             leave_status_obj  = LeaveStatusLog.objects.filter(leave_request=leave_record, leave_level=levels.leave_level,is_approve__isnull=False)
#             if leave_status_obj.exists():
#                 if levels.assigned_to_id == leave_status_obj[0].added_by_id:
#                     rem_items.append(levels.assigned_to_id)
#                 else:
#                     continue
#             else:
#                 rem_items.append(levels.assigned_to_id) 
#             dict_data['name'] = levels.assigned_to.first_name+ " " + levels.assigned_to.last_name
#             dict_data['profile_image'] = levels.assigned_to.profile.profile_pic.url if levels.assigned_to.profile.profile_pic else ''
            
#             # result.append(dict_data)
#             # dict_data = {}
#             pdb.set_trace()
#             i = 1 
#             if leave_status:
#                 for status in leave_status:
#                     i += 1
#                     if status.added_by == levels.assigned_to and status.is_approve:
#                         dict_data['status'] = 'complete'
#                     elif status.added_by == levels.assigned_to and status.is_approve == False:
#                         dict_data['status'] = 'reject'
#                     elif status.added_by == levels.assigned_to:
#                         dict_data['status'] = 'pending'
#                     # leave_status = leave_status.exclude(added_by=status.added_by)
#             else:
#                 dict_data['status'] = 'pending'
#             if levels.leave_level:
#                 dict_data['level'] = levels.leave_level.id
#                 result.append(dict_data)
#             else:
#                 no_level.append(dict_data)
            
#             dict_data = {}
#         pdb.set_trace()   
#         leave_status = leave_status.exclude(added_by_id__in=rem_items)
#         if leave_status:
#             for status in leave_status:
#                 dict_data = {}
#                 dict_data['name'] = status.added_by.first_name + " " + status.added_by.last_name 
#                 dict_data['profile_image'] = status.added_by.profile.profile_pic.url if status.added_by.profile.profile_pic else ''
#                 dict_data['status'] = 'complete' if status.is_approve else 'reject' if status.is_approve == False else 'pending'
#                 if status.leave_level:
#                     dict_data['level'] = status.leave_level.id
#                     result.append(dict_data)
#                 else:
#                     no_level.append(dict_data)
#     if result:
#         result = sorted(result, key=lambda x: x['level'])
#     if no_level:
#         for no_level_list in no_level:
#             result.append(no_level_list)
#     pdb.set_trace()
#     return result


@register.simple_tag
def get_leave_calculation(user, leave_type_id):
    available_leaves, filt_policy, result = 0, {}, {}
    if not user.is_superuser:
        leave_policy = user.profile.leave_policy
        #1. CALCULATE AVAILABLE LEAVE
        if leave_type_id != 2:
            today = date.today()
            joining_date = user.profile.date_of_joining
            leaveadjstmnt = LeaveAdjustment.objects.filter(assigned_to_id=user.id, leave_type_id=leave_type_id).last()
            probation = user.profile.leave_policy.probation_period
            diff_months = (relativedelta.relativedelta(today, joining_date))
            diff_months = diff_months.months + (diff_months.years * 12)
            if (user.profile.hq_employee and user.profile.employement_type == 2):
                stage = 'after-probation'
            else:
                if diff_months >= probation and diff_months < 12:
                    stage = 'probation'
                elif diff_months >= 12:
                    stage = 'after-probation'
                elif diff_months < probation:
                    stage = 'before-probation'

            if leaveadjstmnt:
                filt_policy['adjusted_leave_id'] = leaveadjstmnt.id
                joining_date = leaveadjstmnt.adjusted_date
                new_diff = (relativedelta.relativedelta(today, joining_date))
                new_diff = new_diff.months + (new_diff.years * 12)
                func_response = UserTotalLeave(str(leave_type_id), leave_policy, new_diff, stage)
                if func_response['status']:
                    if leave_type_id == 1:
                        available_leaves = func_response['available_leaves'] + leaveadjstmnt.leave_count
                    else:
                        available_leaves = leaveadjstmnt.leave_count
            else:
                func_response = UserTotalLeave(str(leave_type_id), leave_policy, diff_months, stage)
                if func_response['status']:
                    available_leaves = func_response['available_leaves']
                if leave_type_id == 3:
                    filt_policy['year'] = date.today().year
                
            available_leaves = RemPoints(available_leaves)
        else:
            available_leaves = '--'
        
        #2. CALCULATE TAKEN LEAVE
        leavesHist = UserPolicyLeave.objects.filter(user=user, leave_type_id=leave_type_id, leave_policy=leave_policy, **filt_policy).order_by('id').last()
        taken_leaves = leavesHist.leaves_taken if leavesHist else 0
        taken_leaves = RemPoints(taken_leaves)

        #3. CALCULATE REMAINING LEAVE
        if available_leaves != '--':
            rem_leave = available_leaves - taken_leaves
        else:
            rem_leave = '--'

    result['total_leave'], result['taken_leaves'], result['rem_leave'] = available_leaves, taken_leaves, rem_leave
    return result


@register.simple_tag
def get_course_doc_count(contents):
    data = []
    video , image, document, link = 0, 0 ,0 ,0
    for content in contents:
        if content.file_type == 1:
            video += 1
        elif content.file_type == 2:
            image += 1
        elif content.file_type == 3:
            document += 1
        elif content.file_type == 4:
            link += 1
    return [{'file_type':'Video', 'count':video}, {'file_type':'Image', 'count':image}, {'file_type':'Document', 'count':document}, {'file_type':'Link', 'count':link}]


@register.simple_tag
def is_date_expiry(value):
    if value:
        return datetime.now().strftime("%Y-%m-%d") > value.strftime("%Y-%m-%d")
    return False

@register.filter
def get_nalionality(val):
    if val:
        country_obj = Countries.objects.filter(name=val)
        if country_obj.exists():
            if country_obj[0].nationality:
                return country_obj[0].nationality
            else:
                return country_obj[0].name
    else:
        return "---"

@register.filter
def get_certificate_date_filetr(val):
    if val:
        return val.strftime("%B	%d, %Y")
    

# get_item for column label
@register.filter
def get_item(dictionary, key):
    return dictionary.get(key)

# kpi level form
@register.simple_tag
def get_kpilevel_employee_id(leave_permset, level_id):
    if leave_permset:
        for perm_set in leave_permset:
            if perm_set.kpilevel_id.id == level_id:
                if perm_set.assigned_to:
                    return perm_set.assigned_to_id
        return None
    else:
        return None
    

@register.simple_tag
def get_kpilevel_employee(leave_permset, level_id):
    if leave_permset:
        for perm_set in leave_permset:
            if perm_set.kpilevel_id.id == level_id:
                if perm_set.assigned_to:
                    return perm_set.assigned_to
                    # return perm_set.assigned_to.first_name + " " + perm_set.assigned_to.last_name
        return "--"
    else:
        return "--"

# appraisal level form
@register.simple_tag
def get_appraisal_employee_id(leave_permset, level_id):
    if leave_permset:
        for perm_set in leave_permset:
            if perm_set.appraisal_level.id == level_id:
                if perm_set.assigned_to:
                    return perm_set.assigned_to_id
        return None
    else:
        return None

# show user datas 
@register.simple_tag
def get_appraisal_employee(leave_permset, level_id):
    if leave_permset:
        for perm_set in leave_permset:
            if perm_set.appraisal_level.id == level_id:
                if perm_set.assigned_to:
                    return perm_set.assigned_to.first_name + " " + perm_set.assigned_to.last_name
        return "--"
    else:
        return "--"
    
@register.filter(name='custom_name_filter')
def custom_name_filter(value):
    if value:
        latest_actor_id = ActivityLog.objects.filter(data__id__contains=value.id, content_type=141,action_type='Update'
        ).order_by('-action_time').last()
        if latest_actor_id is not None:
            latest_actor = latest_actor_id.actor.first_name+' '+latest_actor_id.actor.last_name
        else:
            latest_actor = None
        return latest_actor
    return "--"

@register.filter(name='date_format')
def date_format(value):
    if value:
        val = value.strftime('%d %b %Y, %I:%M %p')
        return val
    else:
        return "--"
    
@register.filter()
def time_uae_format(value):
    local_timezone = zoneinfo.ZoneInfo(UAE_TIMEZONE)
    updated_at = value.astimezone(local_timezone)
    if value:
        return updated_at.strftime("%d %B %Y, %H:%M:%S") 
    
@register.filter(name='get_length')
def get_length(value):
    if value:
        return len(value)
    else:
        return 0

@register.simple_tag
def get_kpidefaultlevel_employee(user_id):
    kpi_action_perm = KpiActionPermission.objects.select_related('user','kpi_action','assigned_to').filter(user_id=user_id,kpi_action__in=[5,6])
    if kpi_action_perm:
        kpi_action_perm = kpi_action_perm.last()
        return kpi_action_perm.assigned_to.first_name +' '+kpi_action_perm.assigned_to.last_name
    else:
        return "--"

@register.filter
def get_type(value):
    return str(type(value).__name__)

@register.filter
def get_int(value):
    try:
        return int(value)
    except (ValueError, TypeError):
        return 0 
    
@register.simple_tag
def check_apiaprove_for_user(qryset,userid):
    user_ids = [permission.approved_rejected_user.id for permission in qryset]
    if userid in user_ids:
        return True
    else:
        return False
    
@register.filter(name='getclass')
def getclass(value):
    if value == 'approved':
        clasName = 'approve-complete'
    elif value == 'rejected':
        clasName = 'approve-reject'
    else:
        clasName = 'approve-pending'

    return clasName

@register.simple_tag
def get_presentornot(asignelist, timeperiod):
    user_timeperiod = [user.kpiuser_timeperiod.id for user in asignelist ]
    if timeperiod.id in user_timeperiod:
       return True
    else:
       return False


@register.simple_tag
def get_last_quater(asignelist):
    asignelist = asignelist.last()
    if asignelist:
       return asignelist
    else:
       return False

@register.filter
def stripspaces(value):
    return value.strip()


@register.filter(name='getCapitialized')
def getCapitialized(value):
    capitalized_string = value[0].upper() + value[1:]
    return capitalized_string

@register.simple_tag
def get_timeperiod_status(value,user):
    kpi_usr_timeperiod = KpiUserTimePeriod.objects.select_related('kpi_user_year','kpi_user_year__user','kpi_time_period').filter(kpi_user_year__user_id__in=user,kpi_approval_status__in=['inprogress'],kpi_time_period=value)
    if len(kpi_usr_timeperiod) > 0:
        return  1
    else:
        return 0
    
@register.simple_tag
def get_goal_apprv_stat(value,user):
    kpi_user_year = KpiUserYear.objects.select_related('user','kpi_year').filter(user__in=user,kpi_year__current_year=True)
    for obj in kpi_user_year:
        if obj.status == 2:
            return 1
    return 0



   
    
@register.simple_tag
def get_kpivalues(user,goal):
    kpi_user_goal = KpiUserGoal.objects.select_related('kpi_user_year','kpi_user_year__user','kpi_goal').filter(kpi_user_year__user=user, kpi_goal=goal,is_active=True).last()
    if kpi_user_goal:
        return {
            'goal_value': kpi_user_goal.goal_value,
            'max_finish_rate': kpi_user_goal.max_finish_rate,
            'weight': kpi_user_goal.weight,
        }
    else:
        return {
            'goal_value': None,
            'max_finish_rate': None,
            'weight': None,
        }
    
@register.simple_tag
def get_no_data(userlist,key):
    data_flag = 0
    for usr in userlist:
        if usr.kpiuser_timeperiod.kpi_time_period.id  == key:
            data_flag = 1
            break
    return data_flag

@register.simple_tag
def chek_no_data(userlist,key):
    data_flag = 0
    for usr in userlist:
        if usr.kpi_user_year.kpi_year.id  == key:
            data_flag = 1
            break
    return data_flag
       
@register.simple_tag
def check_equal_string(receiver_name,login_firname,login_lastname):
    login_name = login_firname+' '+login_lastname
    if login_name in receiver_name.split(', '):
        return  1
    else:
        return 0
    
       
# probation level form
@register.simple_tag
def get_probationlevel_employee_id(level_permset, level_id):
    if level_permset:
        for perm_set in level_permset:
            if perm_set.probation_level_id == level_id:
                if perm_set.assigned_to:
                    return perm_set.assigned_to_id
        return None
    else:
        return None
    
@register.simple_tag
def get_probationlevel_employee(level_permset, level_id):
    if level_permset:
        for perm_set in level_permset:
            if perm_set.probation_level_id == level_id:
                if perm_set.assigned_to:
                    return perm_set.assigned_to
                    # return perm_set.assigned_to.first_name + " " + perm_set.assigned_to.last_name
        return "--"
    else:
        return "--"

@register.simple_tag
def get_previous_level_user(queryset,level_id) : 
    previous_level_user = queryset.select_related(
                                        'added_by',
                                        'added_by__profile',
                                        'user',
                                        'user__profile',
                                        'user__profile__designation',
                                        'probation_form',
                                        'probation_level'
                                    ).filter(probation_level_id=level_id).first()
    return previous_level_user.added_by_id if previous_level_user.added_by else None


@register.simple_tag
def get_user_flight_tkt_calculation(user, date_of_joining):
    result = {}
    today = date.today()
    joining_date = date_of_joining
    if joining_date:        
        completed_year = (today - joining_date).days // 365
        if completed_year < 1:
            total_ticket = 0 
        else:
            total_ticket = completed_year # eligible ticket -> approved+pending
        
        prev_ticket = FlightTicket.objects.select_related('sender').filter(sender=user,status__in=['approved','pending','in progress'])
        if prev_ticket:
            # taken_ticket = prev_ticket.count() # availed/used tickets
            taken_ticket = prev_ticket.aggregate(taken_ticket=Sum('extra_ticket'))['taken_ticket']
        else: 
            taken_ticket = 0 # availed/used tickets
        
        ticket_adjusted = FlightTicketAdjustment.objects.select_related('assigned_to', 'added_by').filter(assigned_to_id=user).last()
        if ticket_adjusted:
            total_ticket = ticket_adjusted.ticket_count # eligible ticket -> approved+pending
            adj_date = ticket_adjusted.adjusted_date
            taken_ticket_count = FlightTicket.objects.select_related('sender').filter(sender=user,status__in=['approved','pending','in progress'],requested_date__gte = adj_date)
            if taken_ticket_count:
                takencount = taken_ticket_count.aggregate(taken_ticket=Sum('extra_ticket'))['taken_ticket']
            else:
               takencount = 0 
            if ticket_adjusted.availed_ticket:
                adj_count = ticket_adjusted.availed_ticket
            else:
                adj_count = 0
            prev_ticket = adj_count + takencount
            
            # prev_ticket = ticket_adjusted.availed_ticket # eligible ticket -> approved+pending
            # # prev_ticket = FlightTicket.objects.select_related('sender').filter(sender=user,status__in=['approved','pending','in progress'],requested_date__gte = adj_date)
            if prev_ticket:
                # taken_ticket = prev_ticket.count()
                # taken_ticket = prev_ticket.aggregate(taken_ticket=Sum('extra_ticket'))['taken_ticket']
                taken_ticket = prev_ticket
            else:
                taken_ticket = 0 # availed/used tickets 
        
    else:
        total_ticket = 0
        taken_ticket = 0
    
    # to avoid -ve values
    if total_ticket >= 0:
        total_ticket = total_ticket
    else:
        total_ticket = 0
        
    if taken_ticket >= 0:
        taken_ticket = taken_ticket
    else:
        taken_ticket = 0
        
                
    result['total_ticket'], result['taken_ticket'] = int(total_ticket), int(taken_ticket)
    return result
@register.filter()
def time_format(value):
    now = timezone.now()
    local_timezone = zoneinfo.ZoneInfo(UAE_TIMEZONE)
    created_at = value.astimezone(local_timezone)
    if value:
        if value.strftime("%Y-%m-%d") == now.strftime("%Y-%m-%d"):
            difference = now - value
            hours = difference.days * 24 + difference.seconds // 3600
            minutes = (difference.seconds % 3600) // 60
            if difference.days == 0 and hours == 1:
                return "{0} hour ago".format(hours)
            elif difference.days == 0 and hours >= 1:
                return "{0} hours ago".format(hours)
            elif difference.days == 0 and hours < 1 and minutes == 1:
                return "{0} minute ago".format(minutes)
            elif difference.days == 0 and hours < 1 and minutes != 0:
                return "{0} minutes ago".format(minutes)
            elif difference.days == 0 and hours == 0 and minutes == 0:
                return "Just now"
            else:
                return created_at.strftime("%d %b %Y %I %p")
        else:
            return created_at.strftime("%d %b %Y %I %p")
@register.simple_tag
def get_previous_user_status(queryset, level_id) :
    previous_level_user = queryset.filter(probation_level_id=level_id).first()
    print(previous_level_user.id)
    obj = queryset.select_related(
                                    'added_by',
                                    'added_by__profile',
                                    'user',
                                    'user__profile',
                                    'user__profile__designation',
                                    'probation_form',
                                    'probation_level'
                                ).filter(added_by_id=previous_level_user.added_by_id).first()
    print(obj)
    if obj.approval_status == "pending" and obj.is_approve == False : 
        print(obj.approval_status)
        return False
    elif obj.approval_status == "approved" and obj.is_approve == True : 
        print(obj.approval_status)
        return True
    elif obj.approval_status == "rejected" and obj.is_approve == False : 
        print(obj.approval_status)
        return False

@register.simple_tag
def get_flight_ticket_level_employee(level_permset, level_id):
    if level_permset:
        for perm_set in level_permset:
            if perm_set.ticket_level_id == level_id:
                if perm_set.assigned_to:
                    return perm_set.assigned_to
                    # return perm_set.assigned_to.first_name + " " + perm_set.assigned_to.last_name
        return "--"
    else:
        return "--"
    
@register.simple_tag
def get_flight_ticket_level_employee_id(level_permset, level_id):
    if level_permset:
        for perm_set in level_permset:
            if perm_set.ticket_level_id == level_id:
                if perm_set.assigned_to:
                    return perm_set.assigned_to_id
        return None
    else:
        return None
    
@register.simple_tag
def get_previous_status(queryset, ticket_levels_id) :
    previous_level_user = queryset.filter(ticket_levels_id=ticket_levels_id).first()

    obj = queryset.select_related(
                                    'added_by',
                                    'added_by__profile',
                                    'user',
                                    'user__profile',
                                    'user__profile__designation',
                                    'ticket_request',
                                    'ticket_levels'
                                ).filter(added_by_id=previous_level_user.added_by_id).first()

    if obj.status == "pending" and obj.is_approve == False : 
        return False
    elif obj.status == "approved" and obj.is_approve == True : 
        return True
    elif obj.status == "rejected" and obj.is_approve == False : 
        return False
    

@register.simple_tag
def get_button_status(datas,logged_usr,year,type):
    if type == 'kpi':
        kpi_approval = KPIApprovalStatus.objects.select_related('approved_rejected_user','kpiuser_timeperiod__kpi_time_period','added_by','kpilevel_id').\
            filter(approved_rejected_user=datas,kpiuser_timeperiod__kpi_time_period=year,action_type=KPI)
    elif type == 'goal':
        kpi_approval = KPIApprovalStatus.objects.select_related('approved_rejected_user','kpi_user_year__kpi_year','added_by','kpilevel_id').\
        filter(approved_rejected_user=datas,kpi_user_year__kpi_year=year,action_type=GOAL)
    logined_user_kpi_approval = kpi_approval.filter(added_by = logged_usr)
    logined_user_level_id = logined_user_kpi_approval.last().kpilevel_id_id
    logined_user_level_is_active = logined_user_kpi_approval.last().is_approve
    prev_level = kpi_approval.filter(kpilevel_id_id = int(logined_user_level_id-1)).last()
    next_level = kpi_approval.filter(kpilevel_id_id = int(logined_user_level_id+1)).last()

    if next_level:
        next_level_is_active = next_level.is_approve
    else:
        next_level_is_active = False
    
    if prev_level:
        prev_level_is_active = prev_level.is_approve
    else:
        prev_level_is_active = True

    if logined_user_level_id == 2 and logined_user_level_is_active ==False and next_level_is_active == False:
        button_text = 'Approve/Reject'
    elif prev_level_is_active == True and logined_user_level_is_active ==False and next_level_is_active == False:
        button_text = 'Approve/Reject'
    else:
        button_text = 'View'
    return button_text

@register.simple_tag
def get_probation_level_button(queryset,obj_id,current_user) : 
    button_text = None
    current_user_obj = queryset.select_related("user","added_by").filter(id=obj_id,added_by=current_user).first()
    prev_level = current_user_obj.probation_level_id - 1
    prev_lvl_obj = queryset.select_related("user","probation_level").filter(user_id=current_user_obj.user_id,probation_level_id=prev_level).first()
    if prev_level : 
        if current_user_obj.approval_status == "pending" and current_user_obj.probation_level_id == 2 and prev_lvl_obj.approval_status == "approved" : 
            button_text = "Confirm"
        elif current_user_obj.approval_status == "pending" and current_user_obj.probation_level_id > 2 and prev_lvl_obj.approval_status == "approved" : 
            button_text = "Approve/Reject"
        else : 
            button_text = "View"
    else : 
        if current_user_obj.approval_status == "pending" : 
            button_text = "Approve/Reject"
        else : 
            button_text = "View"
    return button_text

# appraisal data exist or not
@register.simple_tag
def chek_appraisal_data(userlist,key):
    data_flag = 0
    for usr in userlist:
        if usr.appraisal_user_year.kpi_year.id  == key:
            data_flag = 1
            break
    return data_flag

@register.simple_tag
def get_appraisal_status(appraisal_approval_statuses_list,login_user, year, aprovl_user):
    approved = []
    pending = []
    rejected = []
    level_data_count = 0
    for data in appraisal_approval_statuses_list:
        if data.appraisal_user_year.kpi_year.id  == year and data.user == aprovl_user and data.added_by == login_user:
            if data.approval_status == 'rejected':
                rejected.append(data.appraisal_level.name)
                break
            elif data.approval_status == 'pending':
                pending.append(data.appraisal_level.name)
                break
            elif data.approval_status == 'approved':
                approved.append(data.appraisal_level.name)

            level_data_count+=1

    result = {
            'status': 'unknown',
            'levels': [],
    }
    if rejected:
        result['status'] = 'rejected'
        result['levels'] = data.appraisal_level.name
    elif pending:
        result['status'] = 'pending'
        result['levels']= data.appraisal_level.name
    elif len(approved) == level_data_count:
        result['status'] = 'approved'
        # result['approved_levels'].append(data.appraisal_level.name)

    print(result,'---result')
    return result

@register.simple_tag
def get_appraisal_button_status(datas,logged_usr,year,type):
    appraisal_approval = AppraisalApprovalStatus.objects.select_related('user','appraisal_user_year__kpi_year','added_by','appraisal_level').\
    filter(user=datas,appraisal_user_year__kpi_year=year)
    logined_user_kpi_approval = appraisal_approval.filter(added_by = logged_usr)
    logined_user_level_id = logined_user_kpi_approval.first().appraisal_level_id
    logined_user_level_is_active = logined_user_kpi_approval.last().is_approve
    prev_level = appraisal_approval.filter(appraisal_level_id = int(logined_user_level_id-1)).last()
    next_level = appraisal_approval.filter(appraisal_level_id = int(logined_user_level_id+1)).last()
    if len(logined_user_kpi_approval) > 1:
        apprv = logined_user_kpi_approval.filter(is_approve=False)
        if apprv:
            logined_approval_status = apprv.first()
            logined_user_level_id = logined_approval_status.appraisal_level_id
            logined_user_level_is_active = logined_approval_status.is_approve

    if next_level:
        next_level_is_active = next_level.is_approve
    else:
        next_level_is_active = False
    
    if prev_level:
        prev_level_is_active = prev_level.is_approve
    else:
        prev_level_is_active = True
    print(next_level_is_active,'next_level_is_active')
    if logined_user_level_id == 7 and logined_user_level_is_active ==False and next_level_is_active == False:
        button_text = 'Approve/Reject'
    elif prev_level_is_active == True and logined_user_level_is_active ==False and next_level_is_active == False:
        button_text = 'Approve/Reject'
    else:
        button_text = 'View'
    return button_text

@register.simple_tag
def get_appraisal_level_status(user):
    btn_status = AppraisalLevelPermissions.objects.select_related('appraisal_user').filter(appraisal_user=user).only()
    if btn_status:
        return True
    else:
        return False
    

@register.filter(name='date_only_format')
def date_only_format(value):
    if value:
        val = value.strftime('%b %d, %Y')
        return val
    else:
        return "--"

@register.simple_tag
def get_ticket_uploaded_count(ticket_id) : 
    file_obj = FlightTicketAttachments.objects.select_related('flight_ticket').filter(flight_ticket_id=ticket_id,file_type=2)
    count = len(file_obj)
    return count

@register.simple_tag
def get_approval_status_inactive(dict,userid,text):
    if text == 'flght':
        return dict[userid].status
    if text == 'prob':
        return dict[userid].probation_status
    if text == 'apprsl':
        return dict[userid].probation_status
    if text == 'goal' or text == 'kpi':
        return dict[userid].status

@register.simple_tag 
def get_flight_ticket_level_button_text(quryset,current_user,ticket_id) : 
    button_text = None
    prev_level = None
    current_user_obj = quryset.select_related("user","added_by").filter(ticket_request_id=ticket_id,added_by=current_user).first()
    if current_user_obj :
        prev_level = current_user_obj.ticket_levels_id - 1
    prev_level_obj = quryset.select_related("user","added_by").filter(ticket_request_id=ticket_id,ticket_levels_id=prev_level).first()
    if is_admin_user(current_user) and not current_user_obj:
        button_text = 'View Details'
    else :
        if current_user_obj and current_user_obj.status == "pending" and prev_level_obj and prev_level_obj.status == "approved" : 
            button_text = 'Approve/Reject'
        elif not prev_level_obj and current_user_obj and current_user_obj.status == "pending" :
            button_text = 'Approve/Reject'
        else :
            button_text = 'View Details'
    return button_text


# my kpi approved or not status
@register.simple_tag
def get_any_approved(kpiusertimeperiod_set):
    last_period = kpiusertimeperiod_set.last()
    if last_period and last_period.kpi_approval_status == 'approved':
        return True
    return False

# Format value with percentage symbol based on in_percentage flag
@register.filter
def format_with_percentage(value, show_percentage):
    """
    Format a value with or without percentage symbol based on the show_percentage flag.
    Usage: {{ value|format_with_percentage:show_percentage }}
    """
    if value is None or value == '':
        return "N/A"

    try:
        # Convert to float to handle both string and numeric values
        numeric_value = float(value)
        if show_percentage:
            return f"{numeric_value}%"
        else:
            return str(numeric_value)
    except (ValueError, TypeError):
        # If conversion fails, return the original value
        return str(value)

# Get individual percentage flags for actual and target values
@register.simple_tag
def get_percentage_flags(kpi_goal_value):
    """
    Get individual percentage flags for actual and target values from related KpiUserActual and KpiUserTarget records.
    Returns a dictionary with actual_percentage and target_percentage flags.
    """
    from ..models import KpiUserActual, KpiUserTarget

    result = {
        'actual_percentage': kpi_goal_value.in_percentage,  # Default fallback
        'target_percentage': kpi_goal_value.in_percentage   # Default fallback
    }

    try:
        # Try to get the specific percentage flags from KpiUserActual and KpiUserTarget
        if hasattr(kpi_goal_value, 'kpiuser_timeperiod') and hasattr(kpi_goal_value, 'kpiuser_goal'):
            # Get KpiUserActual record
            actual_record = KpiUserActual.objects.filter(
                kpi_user_time_period=kpi_goal_value.kpiuser_timeperiod,
                kpi_user_goal=kpi_goal_value.kpiuser_goal
            ).first()

            if actual_record:
                result['actual_percentage'] = actual_record.in_percentage

            # Get KpiUserTarget record
            target_record = KpiUserTarget.objects.filter(
                kpi_user_time_period=kpi_goal_value.kpiuser_timeperiod,
                kpi_user_goal=kpi_goal_value.kpiuser_goal
            ).first()

            if target_record:
                result['target_percentage'] = target_record.in_percentage

    except Exception:
        # If anything fails, use the default from KPIUserGoalValues
        pass

    return result
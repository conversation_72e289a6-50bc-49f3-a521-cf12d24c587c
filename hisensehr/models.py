from django.db import models
from django.contrib.auth.models import User, Group 
from django.conf import settings
from autoslug import <PERSON>SlugField
from django.contrib.contenttypes.models import ContentType
from django.db.models.signals import post_save,post_delete,pre_delete, pre_save
from django.dispatch import receiver
from phonenumber_field.modelfields import PhoneNumberField
from .validators import validate_possible_number
from django.contrib.auth.models import AbstractUser
from django.contrib.auth.base_user import BaseUserManager
from django.utils.translation import gettext as _
import os
from django.contrib.contenttypes.fields import GenericForeignKey

# Create your models here.

MARITAL_STATUS = (("1", "Single"), ("2", "Married"))
GENDER_CHOICE = (("1", "Male"),("2", "Female"), ("3", "Other"))
VISA_TYPES =((1, 'Business visa'),(2, 'Tourist visa'),(3, 'Transit visa'),(4, 'Student visa'))
KNOWLEDGE_LEVEL =((1, 'Basic'),(2, 'Intermediate'),(3, 'Expert'))
ADDRESS_TYPE =((1, 'Current'),(2, 'Permanent'))

def delete_files(path):
    if os.path.isfile(path):
        os.remove(path)


class PossiblePhoneNumberField(PhoneNumberField):
    """Less strict field for phone numbers written to database."""
    default_validators = [validate_possible_number]



class Countries(models.Model):
    name = models.CharField(max_length=100, blank=True)
    iso3 = models.CharField(max_length=100, blank=True)
    numeric_code = models.CharField(max_length=3, blank=True)
    iso2 = models.CharField(max_length=2, blank=True)
    phonecode = models.CharField(max_length=225)
    capital = models.CharField(max_length=225)
    # currency = models.CharField(max_length=225)
    currency_name = models.CharField(max_length=225)
    currency_symbol = models.CharField(max_length=225)
    tld = models.CharField(max_length=225)
    native = models.CharField(max_length=225, null=True)
    region = models.CharField(max_length=225)
    subregion = models.CharField(max_length=225)
    timezones = models.TextField()
    translations = models.TextField()
    latitude = models.DecimalField(max_digits=90, decimal_places=8)
    longitude = models.DecimalField(max_digits=180, decimal_places=8)
    emoji = models.CharField(max_length=191)
    emoji_u = models.CharField(max_length=191)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    flag = models.BooleanField(default=True)
    wiki_data_id = models.CharField(max_length=225, blank=True, null=True)
    nationality = models.CharField(max_length=225,blank=True,null=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ('name', )
    
    def __str__(self):
        return self.name


class Currency(models.Model):
    country = models.ForeignKey(Countries, on_delete=models.CASCADE, null=True, blank=True)
    title = models.CharField(max_length=200, blank=True, null=True)
    currency_code = models.CharField( max_length=200, blank=True, null=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)

    class Meta:
        verbose_name = 'Currency'
        verbose_name_plural = 'Currency'


class Designation(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    sort_order = models.PositiveIntegerField(blank=True, null=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField( auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    
    class Meta:
        verbose_name = 'Designation'
        verbose_name_plural = 'Designation'


class Department(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    sort_order = models.PositiveIntegerField(blank=True, null=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    
    class Meta:
        verbose_name = 'Department'
        verbose_name_plural = 'Department'

    def __str__(self):
        return self.name


class JobTypes(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True) 
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)

    class Meta:
        verbose_name = 'Job Types'
        verbose_name_plural = 'Job Types'

    def __str__(self):
        return self.name

class UserStatus(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True) 
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    
    class Meta:
        verbose_name = 'User Status'
        verbose_name_plural = 'User Status'


class Branch(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True) 
    country = models.ForeignKey(Countries, on_delete=models.CASCADE, null=True, blank=True)
    location = models.CharField(max_length=200, blank=True, null=True) 
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)

    class Meta:
        verbose_name = 'Branch'
        verbose_name_plural = 'Branch'
    def __str__(self):
        return self.name

class EmployeeVisaStatus(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True) 
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    
    class Meta:
        verbose_name = 'Employee Visa Status'
        verbose_name_plural = 'Employee Visa Status'


class CustomUserManager(BaseUserManager):
    def create_user(self, email, password, **extra_fields):
        if not email:
            raise ValueError(_('The Email must be set'))
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, password, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Superuser must have is_staff=True.'))
        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_('Superuser must have is_superuser=True.'))
        return self.create_user(email, password, **extra_fields)

class User(AbstractUser):
    first_name = models.CharField(max_length=200, blank=True, null=True)
    last_name = models.CharField(max_length=200, blank=True, null=True)
    username = models.CharField(max_length=200, blank=True, null=True)
    email = models.EmailField(verbose_name='email address', max_length=255, unique=True)
    last_login = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    is_superuser = models.BooleanField(default=False)
    date_joined = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    is_staff = models.BooleanField(default=True,null=True,blank=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)


    USERNAME_FIELD = 'email'
    EMAIL_FIELD = 'email'
    REQUIRED_FIELDS = ['password']

    objects = CustomUserManager()

    def __str__(self):
        return self.email + ' - ' + self.first_name + ' ' + self.last_name

    class Meta:
        indexes = [models.Index(fields=['first_name', ]),
                   models.Index(fields=['email', ])]

class HolidayPolicy(models.Model):
    name = models.CharField("Title", max_length=200, blank=True, null=True)
    start_date = models.DateTimeField(null=True, blank=True)
    end_date = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.name


class LeavePolicy(models.Model):
    name = models.CharField(max_length=250, null=False, blank=False)
    start_date = models.DateTimeField(null=True, blank=True)
    end_date = models.DateTimeField(null=True, blank=True)
    probation_period = models.FloatField(null=True, blank=True)
    carryforward = models.BooleanField(default=True)
    holiday_policy = models.ForeignKey(HolidayPolicy, on_delete=models.CASCADE, default=None, related_name='related_holiday_policy')
    work_start_time = models.TimeField(null=True, blank=True)
    work_end_time = models.TimeField(null=True, blank=True)
    days_per_month_before = models.FloatField(null=True, blank=True)
    days_per_month_after = models.FloatField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.name

MENU_TYPES = (
    (1,"HR & Admins"),
    (2,"Marketing"),
    (3,"Finance & Ops Mgmt"),
    (4,"SCM"),
    (5,"Service"),
)

class InusrancePolicy(models.Model):
    name = models.CharField(max_length=250,null=True,blank=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

EMPLOYEMENT_TYPE =(
    (1,'probation'),
    (2,'permanent')
)

class Profile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE,related_name="profile")
    employee_id = models.CharField(max_length=200, blank=True, null=True)
    attendance_id = models.CharField(max_length=200, blank=True,null=True, unique=True)
    nick_name = models.CharField(max_length=200, blank=True, null=True)
    designation_on_visa = models.ForeignKey(Designation, on_delete=models.CASCADE, null=True, blank=True, related_name = "visa_designation")
    designation = models.ForeignKey(Designation, on_delete=models.CASCADE, null=True, blank=True, related_name = "profile_designation")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True)
    location = models.CharField(max_length=200, blank=True, null=True) 
    date_of_birth = models.DateTimeField(null=True, blank=True)
    profile_pic = models.ImageField(upload_to='profile', blank=True, null=True)
    user_time_zone = models.CharField(max_length=200, default='Asia/Dubai', blank=False, null=False)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, null=True, blank=True)
    country = models.ForeignKey(Countries, on_delete=models.CASCADE, null=True, blank=True)
    marital_status = models.PositiveIntegerField(choices=MARITAL_STATUS, default=1, blank=True, null=True) 
    contact_number = PossiblePhoneNumberField(blank=True, default="", db_index=True)
    gender = models.PositiveIntegerField(choices=GENDER_CHOICE, default=1, blank=True, null=True)
    language = models.CharField(max_length=200, blank=True, null=True)
    login_type = models.CharField(max_length=200, blank=True, null=True)
    apple_identifier = models.CharField(max_length=200, blank=True, null=True)
    nationality = models.CharField(max_length=200, blank=True, null=True)
    selected_date = models.DateField(null=True, blank=True)
    date_of_joining = models.DateField(null=True, blank=True)
    date_of_leaving = models.DateField(null=True, blank=True)
    reporting_person = models.ForeignKey(settings.AUTH_USER_MODEL, related_name="user_reporting_person", on_delete=models.CASCADE, null=True, blank=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name="user_profile_created_by" , on_delete=models.CASCADE, null=True, blank=True)
    user_status = models.ForeignKey(UserStatus, on_delete=models.CASCADE, null=True, blank=True,default=1)
    job_status = models.ForeignKey(JobTypes, on_delete=models.CASCADE, null=True, blank=True)
    leave_policy = models.ForeignKey(LeavePolicy, on_delete=models.CASCADE, null=True, blank=True, default=1)
    age = models.CharField(max_length=200, blank=True, null=True)
    blood_group = models.CharField(max_length=200, blank=True, null=True)
    #password_str = models.CharField(max_length=200, blank=True, null=True)
    policy = models.PositiveIntegerField(choices=MENU_TYPES,blank=True, null=True)
    insurance_policy = models.ForeignKey(InusrancePolicy,on_delete=models.CASCADE,null=True,blank=True)
    sap_code = models.CharField(max_length=200,null=True,blank=True)
    designation_title = models.CharField(max_length=200,null=True,blank=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    edit_profile = models.BooleanField(default=True,null=True,blank=True)
    employement_type = models.PositiveIntegerField(choices=EMPLOYEMENT_TYPE,null=False,blank=False,default=2)
    hq_employee = models.BooleanField(default=False,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    workflow_token = models.CharField(max_length=100, null=True, blank=True)

    @receiver(post_save, sender=User)
    def create_user_profile(sender, instance, created, **kwargs):
        if created:
            Profile.objects.create(user=instance)

    # @receiver(post_save, sender=User)
    # def save_user_profile(sender, instance, **kwargs):
    #     instance.profile.save()

    def __str__(self):
        return u'{0} {1} ({2})'.format(self.user.first_name,self.user.last_name,self.user.email)

    class Meta:
        indexes = [models.Index(fields=['employee_id', ])]

class Dynamic_menus(models.Model):
    title = models.CharField(max_length=200, blank=True, null=True)
    icon = models.CharField(max_length=200, blank=True, null=True)
    key_word = models.CharField( max_length=200, blank=True, null=True)
    title_slug = AutoSlugField(populate_from='title', blank=True, null=True, unique=True)
    route_name = models.CharField(max_length=200, blank=True, null=True)
    is_left_menu = models.BooleanField(default=True,null=True,blank=True)
    is_role_access  = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    parent = models.ForeignKey('self', related_name="childs", on_delete=models.CASCADE, null=True, blank=True)
    content_type = models.ForeignKey(ContentType, related_name="sub_menus", on_delete=models.CASCADE, null=True, blank=True)
    sort_order = models.PositiveIntegerField(default=0, blank=False, null=False)

    class Meta:
        verbose_name = 'Dynamic menu'
        verbose_name_plural = 'Dynamic menus'

    def has_group_permission(self,g_id):
        return True

    def __str__(self):
        return u'{0}'.format(self.title)


class Group_menu_access(models.Model):
    group = models.ForeignKey(Group, verbose_name="hisense_group", on_delete=models.CASCADE, null=True, blank=True, related_name='hisense_group')
    dynamic_menu = models.ForeignKey(Dynamic_menus, related_name='menus_access', on_delete=models.CASCADE, null=True,blank=True)

    class Meta:
        verbose_name = 'Group Menu access'
        verbose_name_plural = 'Group Menu access'


class User_menu_access(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    dynamic_menu = models.ForeignKey(Dynamic_menus, related_name='user_menu_access', on_delete=models.CASCADE, null=True, blank=True)

    class Meta:
        verbose_name = 'User Menu access'
        verbose_name_plural = 'User Menu access'
    
class EmailTemplate(models.Model):

	name = models.CharField("Template Name",max_length=255, blank=True) 
	content =  models.TextField("Content source code", blank=True)   
	created_at = models.DateTimeField(auto_now_add=True, blank=True)
	updated_at = models.DateTimeField(auto_now=True, blank=True)
	is_active = models.BooleanField("Active",default=True)
	my_order = models.PositiveIntegerField(default=0, blank=False, null=False,editable=True)

	class Meta:
		verbose_name = 'Email Template'
		verbose_name_plural = 'Email Template'

	def __str__(self):
		return self.name

class EmployeePassportDetails(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,related_name='related_user_passport')
    passport_number = models.CharField( max_length=200, blank=True, null=True)
    date_of_issue = models.DateTimeField( null=True, blank=True)
    date_of_expiry = models.DateTimeField(null=True, blank=True)
    nationality = models.ForeignKey(Countries, on_delete=models.CASCADE, null=True, blank=True)
    place_of_issue  = models.CharField(max_length=200, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    class Meta:
        verbose_name = 'User Passport Details'
        verbose_name_plural = 'User Passport Details'

DOC_TYPES = (
    (1,'passport'),
    (2,'visa'),
    (3,'eid'),
    (4,'graduate'),
    (5,'other'),
    (6,'insurance'),

)

class EmployeeDocuments(models.Model):
    name = models.TextField(null=True,blank=True, max_length=200)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,related_name='related_user_documents')
    doc_type = models.PositiveIntegerField(choices=DOC_TYPES,null=False,blank=False,default=1)
    file = models.FileField(upload_to='user_docs', blank=True, null=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    class Meta:
        verbose_name = 'Employee Documents'
        verbose_name_plural = 'Employee Documents'


class EmployeeVisaDetails(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,related_name='related_user_visa')
    id_number = models.CharField(max_length=200, blank=True, null=True) 
    file_number = models.CharField(max_length=200, blank=True, null=True)
    visa_holders_name = models.CharField(max_length=200, blank=True, null=True)
    visa_status = models.ForeignKey(EmployeeVisaStatus, on_delete=models.CASCADE, null=True, blank=True)
    profession = models.CharField(max_length=200, blank=True, null=True)
    employer = models.CharField(max_length=200, blank=True, null=True)
    issue_date = models.DateTimeField(null=True, blank=True)
    expiry_date = models.DateTimeField(null=True, blank=True)
    visa_duration = models.PositiveIntegerField(blank=True, null=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    class Meta:
        verbose_name = 'Employee Visa Details'
        verbose_name_plural = 'Employee Visa Details'


class EmployeeEIdDetails(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='related_user_eid')
    id_number = models.CharField(max_length=200, blank=True, null=True) 
    card_number = models.CharField(max_length=200, blank=True, null=True) 
    issuing_place = models.CharField(max_length=200, blank=True, null=True)
    date_of_issue = models.DateTimeField(null=True, blank=True)
    expiry_date = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    class Meta:
        verbose_name = 'Employee EID Details'
        verbose_name_plural = 'Employee EID Details'


class EmployeeHealthInsurance(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,related_name='related_user_insurance')
    insurance_network = models.CharField(max_length=200, blank=True, null=True)
    ub_no = models.CharField(max_length=200,blank=True, null=True)
    emp_id = models.CharField(max_length=200,blank=True, null=True)
    policy_year = models.CharField(max_length=200, blank=True, null=True)
    category = models.CharField(max_length=200, blank=True, null=True)
    tpa = models.CharField(max_length=200, blank=True, null=True)
    date_of_issue = models.DateTimeField(null=True, blank=True)
    expiry_date = models.DateTimeField(null=True, blank=True)
    insurance_policy = models.ForeignKey(InusrancePolicy,on_delete=models.CASCADE,null=True,blank=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    class Meta:
        verbose_name = 'Employee Health Insurance'
        verbose_name_plural = 'Employee Health Insurance'

class EmployeeBankDetails(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,related_name='related_user_bank')
    account_holders_name = models.CharField(max_length=200, blank=True, null=True) 
    account_number = models.CharField(max_length=100,blank=True, null=True)
    iban_code = models.CharField(max_length=200, blank=True, null=True)
    bank_name = models.CharField(max_length=200, blank=True, null=True) 
    branch = models.CharField(max_length=200, blank=True, null=True) 
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE, null=True, blank=True)
    account_class_type = models.CharField(max_length=200, blank=True, null=True) 
    city = models.CharField(max_length=200, blank=True, null=True) 
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)


    class Meta:
        verbose_name = 'Employee Bank Details'
        verbose_name_plural = 'Employee Bank Details'


class EmployeeWorkExperience(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,related_name='related_user_work_exp')
    company_name = models.CharField(max_length=200, blank=True, null=True) 
    website = models.CharField(max_length=200, blank=True, null=True) 
    designation = models.CharField(max_length=200, blank=True, null=True) 
    from_date = models.DateTimeField(null=True, blank=True)
    to_date = models.DateTimeField(null=True, blank=True)
    total_month_exp = models.CharField(max_length=200, blank=True, null=True) 
    reason_for_leaving = models.CharField(max_length=200, blank=True, null=True) 
    reference_name = models.CharField(max_length=200, blank=True, null=True) 
    reference_contact = models.CharField(max_length=200, blank=True, null=True) 
    reference_email = models.EmailField(max_length=255, null=True, blank=True)
    row_number = models.PositiveIntegerField(blank=True, null=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)


    class Meta:
        verbose_name = 'Employee Work Experience'
        verbose_name_plural = 'Employee Work Experience'


class EmployeeSkills(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,related_name='related_user_skills')
    name_of_skill = models.CharField(max_length=200, blank=True, null=True) 
    year_of_exp = models.PositiveIntegerField(blank=True, null=True)
    last_used = models.CharField(max_length=200, blank=True, null=True) 
    nlp_trainer = models.BooleanField(default=True,null=True,blank=True)
    certification = models.CharField(max_length=200, blank=True, null=True) 
    institute = models.CharField(max_length=200, blank=True, null=True) 
    row_number = models.PositiveIntegerField(blank=True, null=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)


    class Meta:
        verbose_name = 'Employee Skills'
        verbose_name_plural = 'Employee Skills'


class EmployeeAddress(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,related_name='related_user_address')
    type = models.PositiveIntegerField(choices=ADDRESS_TYPE, default=1, blank=False, null=False)
    street_details = models.TextField(blank=True, null=True)
    city = models.CharField(max_length=200, blank=True, null=True)
    country_text = models.CharField(max_length=200, blank=True, null=True)
    country = models.ForeignKey(Countries, on_delete=models.CASCADE, null=True, blank=True)
    emergency_contact_person = models.CharField(max_length=200, blank=True, null=True)
    relation = models.CharField(max_length=200, blank=True, null=True)
    emergency_contact = models.CharField(null=True, blank=True, max_length=255)
    emergency_email = models.EmailField(max_length=255, null=True, blank=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    class Meta:
        verbose_name = 'Employee Address'
        verbose_name_plural = 'Employee Address'

class EmployeeDependents(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,related_name='related_user_dependents')
    dependent_name = models.CharField(max_length=200, blank=True, null=True)
    relationship = models.CharField(max_length=200, blank=True, null=True)
    in_uae = models.BooleanField(default=True, blank=True, null=True)
    visa_status = models.BooleanField(default=True, blank=True, null=True)
    date_of_birth = models.DateTimeField(null=True, blank=True)
    age = models.PositiveIntegerField(null=True, blank=True)
    is_active = models.BooleanField(default=True, blank=True, null=True)
    row_number = models.PositiveIntegerField(blank=True, null=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    class Meta:
        verbose_name = 'Employee Dependents'
        verbose_name_plural = 'Employee Dependents'

class EmployeeEducation(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,related_name='related_user_education')
    education_level = models.CharField(max_length=200, blank=True, null=True)
    institute_name = models.CharField(max_length=200, blank=True, null=True)
    course = models.CharField(max_length=200, blank=True, null=True)
    from_date = models.DateTimeField(null=True, blank=True) 
    to_date = models.DateTimeField(null=True, blank=True)   
    percentage = models.FloatField(null=True, blank=True)
    grade = models.CharField(max_length=200, blank=True, null=True)
    gpa = models.FloatField(null=True, blank=True)
    row_number = models.PositiveIntegerField(blank=True, null=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    is_highest = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    class Meta:
        verbose_name = 'Employee Education'
        verbose_name_plural = 'Employee Education'


class HeadOfDepartment(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,related_name='related_user_hod')
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True,related_name='head_of_department')
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    class Meta:
        verbose_name = 'Head Of Department'
        verbose_name_plural = 'Head Of Department'

class MeasurementType(models.Model):
    name = models.CharField(max_length=100,null=False,blank=False)
    calculation = models.TextField(null=True,blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

KPI_ACTION_TYPE = (
    (1,"Kpi Basic"),
    (2,"Kpi User"),
    (3,"Appraisal Basic"),
    (4,"Appraisal User"),
)

class KpiAction(models.Model):
    name = models.CharField(max_length=100,null=False,blank=False)
    kpi_action_type = models.PositiveIntegerField(choices=KPI_ACTION_TYPE,default=1)
    is_active = models.BooleanField(default=True)
    key_action = models.CharField(max_length=100,blank=True,null=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class KpiActionPermission(models.Model):
    kpi_action = models.ForeignKey(KpiAction,on_delete=models.CASCADE,null=False,blank=False,related_name="action_permissions")
    assigned_to = models.ForeignKey(User,on_delete=models.CASCADE,null=False,blank=False,related_name="assigned_to_kpi_permissions")
    user = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True,related_name="user_kpi_permissions")
    department = models.ForeignKey(Department,on_delete=models.CASCADE,null=True,blank=True,related_name="department_kpi_permissions")
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)


TIME_PERIODS = (
    (1,"Quarterly"),
    (2,"Half Yearly"),
    (3,"Yearly")
)
class KpiYear(models.Model):
    name = models.CharField(max_length=100,null=False,blank=False)
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=True)
    time_period = models.PositiveIntegerField(choices=TIME_PERIODS,default=1)
    current_year = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class KpiGuideline(models.Model):
    kpi_year = models.OneToOneField(KpiYear,on_delete=models.CASCADE)
    content = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    current_year = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class KpiUserGuideline(models.Model):
    user = models.ForeignKey(User,on_delete=models.CASCADE)
    kpi_year = models.ForeignKey(KpiYear,on_delete=models.CASCADE)
    content = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    current_year = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class KpiTimePeriod(models.Model):
    kpi_year = models.ForeignKey(KpiYear,on_delete=models.CASCADE)
    name = models.CharField(max_length=100,null=True,blank=True)
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class KpiCategory(models.Model):
    name = models.CharField(max_length=500,null=False,blank=False)
    department = models.ForeignKey(Department,on_delete=models.CASCADE,null=False,blank=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class KpiGoal(models.Model):
    name = models.CharField(max_length=1000,null=False,blank=False)
    kpi_category = models.ForeignKey(KpiCategory,on_delete=models.CASCADE, related_name="kpi_goals")
    measurement_type = models.ForeignKey(MeasurementType,on_delete=models.CASCADE)
    added_by = models.ForeignKey(User,on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

# GOAL_STATUS = ((1,'Pending'),
#                 (2,'Active'),
#                 (3,'Employee Approved'),
#                 (4,'HOD Approved'),
#                 (5,'Rejected'))

GOAL_STATUS = ((1,'Pending'),
                (2,'Inprogress'),
                (3,'Approved'),
                (4,'Rejected'),
                )

class KpiUserYear(models.Model):
    kpi_year = models.ForeignKey(KpiYear,on_delete=models.CASCADE)
    user = models.ForeignKey(User,on_delete=models.CASCADE)
    status = models.PositiveIntegerField(choices=GOAL_STATUS,default=1)
    appraisal_status = models.PositiveIntegerField(choices=GOAL_STATUS,default=1)
    approved_by = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True,related_name="goal_approved")
    rejected_by = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True,related_name="goal_rejected")
    rejected_at = models.DateTimeField(null=True,blank=True)
    employee_signature = models.ImageField(upload_to="kpi_signature",null=True,blank=True)
    hod_signature = models.ImageField(upload_to="kpi_signature",null=True,blank=True)
    employee_signature_date = models.DateField(null=True,blank=True)
    hod_signature_date = models.DateField(null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)
    
class KpiUserGoal(models.Model):
    kpi_user_year = models.ForeignKey(KpiUserYear,on_delete=models.CASCADE,null=True,blank=True, related_name='user_year_goals')
    kpi_goal = models.ForeignKey(KpiGoal,on_delete=models.CASCADE)
    goal_value = models.TextField(null=True,blank=True)
    max_finish_rate = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    weight = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    added_by = models.ForeignKey(User,on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class KpiActualTargetPermission(models.Model):
    kpi_user_goal = models.ForeignKey(KpiUserGoal,on_delete=models.CASCADE,related_name="at_goal_permissions")    
    kpi_action = models.ForeignKey(KpiAction,on_delete=models.CASCADE,null=False,blank=False,related_name="at_action_permissions")
    assigned_to = models.ForeignKey(User,on_delete=models.CASCADE,null=False,blank=False,related_name="assigned_to_kpi_at_permissions")
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

KPI_STATUS = (
    (1,"Pending"),
    (2,"Target values are set"),
    (3,"Actual values are set"),
    (4,"Employee Approved"),
    (5,"HOD Approved"),
)

class KpiUserTimePeriod(models.Model):
    kpi_user_year = models.ForeignKey(KpiUserYear,on_delete=models.CASCADE,null=True,blank=True, related_name="user_year_kpi_values")
    kpi_time_period = models.ForeignKey(KpiTimePeriod,on_delete=models.CASCADE)
    status = models.PositiveIntegerField(choices=KPI_STATUS,default=1)
    hod_signature = models.ImageField(upload_to="kpi_signature",null=True,blank=True)
    hod_date = models.DateField(null=True,blank=True)
    approved_by = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True,related_name="kpi_approved")
    employee_signature = models.ImageField(upload_to="kpi_signature",null=True,blank=True)
    employee_signature_date = models.DateField(null=True,blank=True)
    set_target = models.BooleanField(default=False)
    set_actual = models.BooleanField(default=False)
    kpi_approval_status = models.CharField(max_length=200,blank=True,null=True)
    score = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    updated_score = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    performance_comment = models.TextField(blank=True, null=True)

class KpiUserActual(models.Model):
    kpi_user_time_period = models.ForeignKey(KpiUserTimePeriod,on_delete=models.CASCADE,null=True,blank=True)
    kpi_user_goal = models.ForeignKey(KpiUserGoal,on_delete=models.CASCADE,related_name='user_actual')
    actual = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    remark = models.TextField(null=True,blank=True)
    added_by = models.ForeignKey(User,on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)
    in_percentage = models.BooleanField(default=True, help_text="Indicates if actual value should be displayed with % symbol")

class KpiUserTarget(models.Model):
    kpi_user_time_period = models.ForeignKey(KpiUserTimePeriod,on_delete=models.CASCADE,null=True,blank=True)
    kpi_user_goal = models.ForeignKey(KpiUserGoal,on_delete=models.CASCADE,related_name='user_target')
    target = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    remark = models.TextField(null=True,blank=True)
    added_by = models.ForeignKey(User,on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)
    in_percentage = models.BooleanField(default=True, help_text="Indicates if target value should be displayed with % symbol")


class AppraisalCategory(models.Model):
    name = models.CharField(max_length=300,null=False,blank=False)
    department = models.ForeignKey(Department,on_delete=models.CASCADE,null=False,blank=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)


class AppraisalPoint(models.Model):
    name = models.CharField(max_length=250,null=False,blank=False)
    appraisal_category = models.ForeignKey(AppraisalCategory,on_delete=models.CASCADE, related_name="related_appraisal_goals")
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)


APPRAISAL_STATUS = ((1,'Pending'),
        (2,'Inprogress'),
        (3,'Approved')
        )

class UserAppraisalForms(models.Model):
    user = models.ForeignKey(User,on_delete=models.CASCADE, related_name='user_appraisal_forms')
    kpi_year = models.ForeignKey(KpiYear,on_delete=models.CASCADE, default=None, null=True, blank=True)    
    start_date = models.DateField()
    end_date = models.DateField()
    self_review_achievements = models.TextField(blank=True, null=True)    
    improvement_suggestions = models.TextField(blank=True, null=True) 
    advantages = models.TextField(blank=True, null=True) 
    procedures = models.TextField(blank=True, null=True)  
    responsibilities = models.TextField(blank=True, null=True) 
    agree_appraisal_reason = models.TextField(blank=True, null=True) 
    training_need_description = models.TextField(blank=True, null=True) 
    next_year_task = models.TextField(blank=True, null=True)   
    status = models.PositiveIntegerField(blank=True, null=True, default=1)  
    approval_status = models.PositiveIntegerField(choices=APPRAISAL_STATUS,default=1)
    employee_signature = models.ImageField(upload_to="appraisal_signature",null=True,blank=True)
    supervisor_signature = models.ImageField(upload_to="appraisal_signature",null=True,blank=True)
    employee_signature_date = models.DateField(null=True,blank=True)
    supervisor_signature_date = models.DateField(null=True,blank=True)
    approved_by = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True,related_name="apprisal_approved")
    department_ranking = models.CharField(max_length=10,null=True,blank=True) 
    supervisor_appraisal_grade = models.CharField(max_length=10,null=True,blank=True)  
    hr_appraisal_grade = models.CharField(max_length=10,null=True,blank=True)  
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class UserAppraisalPoint(models.Model):
    appraisal_form = models.ForeignKey(UserAppraisalForms,on_delete=models.CASCADE, default=None, related_name = "appraisal_form_points")
    appraisal_point = models.ForeignKey(AppraisalPoint,on_delete=models.CASCADE)
    added_by = models.ForeignKey(User,on_delete=models.CASCADE, default=None, related_name = "added_appraisal_point")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class UserAppraisalEvaluation(models.Model):
    user_point = models.ForeignKey(UserAppraisalPoint,on_delete=models.CASCADE, default=None, related_name = "point_evaluations")
    evaluated_by = models.ForeignKey(User,on_delete=models.CASCADE, related_name = "evaluated_appraisal_points", default=None)
    kpi_action = models.ForeignKey(KpiAction,on_delete=models.CASCADE,null=True,blank=True,default=None,related_name="action_evaluations")
    rating = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class UserAppraisalGrade(models.Model):
    name = models.CharField(max_length=250,null=False,blank=False)
    grade = models.CharField(max_length=3,null=False,blank=False)
    start = models.IntegerField()
    end = models.IntegerField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

EVALUATION_TYPE = ((1, 'Employee'),(2, 'HOD'),)
class UserAppraisalEvaluationTime(models.Model):
    type = models.PositiveIntegerField(choices=EVALUATION_TYPE, default=1, blank=False, null=False)
    kpi_year = models.ForeignKey(KpiYear,on_delete=models.CASCADE, default=None, null=True, blank=True)    
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

class UserAppraisalEvaluationEntry(models.Model):
    type = models.PositiveIntegerField(choices=EVALUATION_TYPE, default=1, blank=False, null=False)
    appraisal_form = models.ForeignKey(UserAppraisalForms,on_delete=models.CASCADE, default=None, null=True, blank=True, related_name = "appraisal_form_except")    
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

APPRAISAL_STATUS = ((1, 'Pending'),(2, 'Waiting for employee evaluation'),
                    (3, 'Waiting for supervisor evaluation'),(4, 'Waiting for supervisor evaluation'),
                    (5, 'Waiting for supervisor evaluation'),(6, 'Waiting for HR review'),
                    (7, 'Waiting for supervisor review'),(8, 'Waiting for employee approval'),
                    (9, 'Waiting for HR approval'),(10, 'HR approved'),
                    )

class UserAppraisalStatusLog(models.Model):
    appraisal_form = models.ForeignKey(UserAppraisalForms,on_delete=models.CASCADE, default=None, related_name = "appraisal_form_statuses")
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    status = models.PositiveIntegerField(choices=APPRAISAL_STATUS, null=True, blank=True,default=1)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

class LeaveType(models.Model):
    name = models.CharField(max_length=250, null=False, blank=False)
    days = models.PositiveIntegerField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.name


class Weekdays(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True, unique=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)

    def __str__(self):
        return self.name



LEAVETYPE = ((1, 'Regional'),(2, 'Global'),)
class Holidays(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True)
    holiday_policy = models.ForeignKey(HolidayPolicy, on_delete=models.CASCADE, default=None, related_name='policy_holidays')
    country = models.ForeignKey(Countries, on_delete=models.CASCADE, blank=True, null=True)
    date = models.DateTimeField(null=True, blank=True) 
    start_date = models.DateTimeField(null=True, blank=True) 
    end_date = models.DateTimeField(null=True, blank=True) 
    days = models.PositiveIntegerField(default=1, blank=True, null=True) 
    occassion = models.CharField(max_length=200, blank=True, null=True)
    restricted_holiday = models.BooleanField(default=False)
    type = models.PositiveIntegerField(choices=LEAVETYPE, default=1, blank=False, null=False)
    year = models.PositiveIntegerField(blank=True, null=True) 
    is_confirm = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)  

    def __str__(self):
        return self.name


class LeavePolicy_Weekdays(models.Model):
    leave_policy = models.ForeignKey(LeavePolicy,null=True,blank=True,on_delete=models.CASCADE,related_name='policy_weekdays')
    weekday = models.ForeignKey(Weekdays,null=False,blank=False,on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)



class LeaveLevels(models.Model):
    name = models.CharField(max_length=250, null=False, blank=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.name


class LeaveSetting(models.Model):
    leave_policy = models.ForeignKey(LeavePolicy, on_delete=models.CASCADE, default=None, related_name='leave_year_setting')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE, default=None)
    no_of_days = models.FloatField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)


class LeavePermission(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='user_leave_permission')
    leave_level = models.ForeignKey(LeaveLevels, on_delete=models.CASCADE, default=None, related_name='leave_permission_level')
    assigned_to = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='assigned_leave_permission')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)



ADJUSTMENT_TYPE = ((1, "addition"),(2, "deduction"))
class LeaveAdjustment(models.Model):
    assigned_to = models.ForeignKey(User,on_delete=models.CASCADE, related_name='apply_adjustment_for')
    added_by = models.ForeignKey(User,on_delete=models.CASCADE, related_name='adjustment_added_by')
    leave_type = models.ForeignKey(LeaveType,on_delete=models.CASCADE, null=True, blank=True, related_name='adjustment_leave_type')
    adjustment_type = models.PositiveIntegerField(choices=ADJUSTMENT_TYPE, default=1)
    leave_count = models.FloatField(null=True, blank=True)
    adjusted_date = models.DateField(null=True, blank=True)
    comment = models.TextField(null=True, blank=True)
    is_auto_adjustment = models.BooleanField(null=True, blank=True,default=False)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)

    

class UserPolicyLeave(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='user_applied_leaves')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE, default=None, related_name='user_leave_type')
    total_leaves = models.FloatField(null=True, blank=True)
    leaves_taken = models.FloatField(null=True, blank=True)
    leave_policy = models.ForeignKey(LeavePolicy, on_delete=models.CASCADE, default=None, related_name='related_policy_leaves')
    adjusted_leave = models.ForeignKey(LeaveAdjustment, on_delete=models.CASCADE, default=None, null=True, blank=True, related_name='user_adjusted_leave')
    year = models.IntegerField(default=None,null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    

LEAVE_REQUEST_STATUS = (
    (1,"Pending"),
    (2,"In-progress"),
    (3,"Approved"),
    (4,"Rejected"),
    (5,"Cancelled"),
    (6,"Expired"),
    (7,"Holiday")
)

LEAVE_DAY_TYPE = (
    (1,"Full Day"),
    (2,"Forenoon"),
    (3,"Afternoon")
)


class LeaveRequest(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name="user_leave_requests")
    start_date = models.DateField(null=True,blank=True)
    end_date = models.DateField(null=True,blank=True)
    reason = models.TextField(blank=True, null=True)
    status = models.PositiveIntegerField(choices=LEAVE_REQUEST_STATUS,default=1)
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE, null=True, blank=True, related_name="leave_request_type")
    day_type = models.PositiveIntegerField(choices=LEAVE_DAY_TYPE,default=1)
    day_count = models.FloatField(null=True,blank=True)
    staff_incharge = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    leave_doc = models.FileField(upload_to='leave_doc',null=True,blank=True)
    cancel_date = models.DateField(null=True,blank=True)
    leave_policy = models.ForeignKey(LeavePolicy, on_delete=models.CASCADE, default=None, related_name='related_requested_leave')
    added_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name="leave_request_created_user")
    remaining_leaves = models.FloatField(null=True,blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)


class LeaveStatusLog(models.Model):
    leave_request = models.ForeignKey(LeaveRequest, on_delete=models.CASCADE, null=True, blank=True, related_name='leave_request_status')
    leave_level = models.ForeignKey(LeaveLevels, on_delete=models.CASCADE, null=True, blank=True)
    is_approve = models.BooleanField(default=True,null=True,blank=True)
    remark = models.TextField(blank=True, null=True)
    added_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)


class AutoLeaveAdjustment(models.Model):
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE, null=True, blank=True,related_name='auto_leave_adjustment_leave_type')
    adjusted_date = models.DateField(null=True,blank=True)
    adjusted_count = models.PositiveIntegerField(null=True,blank=True)
    remark = models.CharField(max_length=500,null=True, blank=True)
    adjusted_by = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True,related_name='auto_leave_adjusted_by')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    
class AutoLeaveAdjustmentLog(models.Model):
    auto_leave_adjustment = models.ForeignKey(AutoLeaveAdjustment, on_delete=models.CASCADE, null=True,\
                                blank=True,related_name='auto_leave_adjustment_logs')
    leave_adjustment = models.ForeignKey(LeaveAdjustment, on_delete=models.CASCADE,null=True,blank=True,related_name='auto_leave_adjustment_leave_adjustment')
    adjusted_to = models.ForeignKey(User, on_delete=models.CASCADE, null=True,blank=True,related_name='auto_leave_adjusted_to')
    total_leave_count_before_adj = models.PositiveIntegerField(null=True,blank=True)
    total_leave_count_after_adj = models.PositiveIntegerField(null=True,blank=True)
    encashed_leave_count = models.PositiveIntegerField(null=True,blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    
    


class Posts(models.Model):
    status_choices = (('Pending','pending'),('Approved','approved'),('Rejected','rejected'),('Deleted','deleted'))
    description = models.TextField()
    user = models.ForeignKey(User,on_delete=models.CASCADE)
    status = models.CharField(max_length=20,choices=status_choices,default='Approved')
    parent_id = models.ForeignKey('self',null=True,blank=True,on_delete=models.CASCADE, related_name="parent_posts")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now_add=True)

def GetLastPK(instance):
    instance_id = 1
    if instance.pk is None:
        instance_last = instance.__class__.objects.last()
        if instance_last != None:
            instance_id = instance_last.pk + 1
    else:
        instance_id = instance.pk
    return instance_id

def posts_file(instance, filename):
    instance_id = GetLastPK(instance)
    ext = filename.split(".")[-1]
    newname = "post_file/" + str( instance_id + 1) + "." + ext
    return newname  

class PostsFiles(models.Model):
    file_type_choices = (('video','video'),('image','image'))
    file = models.FileField(upload_to=posts_file,null=True,blank=True)
    post = models.ForeignKey(Posts,on_delete=models.CASCADE,related_name='posts_files')
    file_type = models.CharField(max_length=20,null=True,blank=True)
    link = models.URLField(null=True,blank=True)

@receiver(signal=post_delete,sender=PostsFiles)
def delete_posts_file(sender,instance,*args,**kwargs):
    if instance.file:
        delete_files(instance.file.path)


class PostLike(models.Model):
    post = models.ForeignKey(Posts,on_delete=models.CASCADE,related_name="posts_likes")
    user = models.ForeignKey(User,on_delete=models.CASCADE)
    like = models.BooleanField(default=False)

    class Meta:
        unique_together = ('post','user')


class Notification(models.Model):
    user_from = models.ForeignKey(User, related_name='user_from', on_delete=models.CASCADE, blank = True, null = True)
    user_to = models.ForeignKey(User, related_name='user_to', on_delete=models.CASCADE, blank= True, null=True)
    message = models.TextField(null=True, blank=True)
    info = models.JSONField(blank=True, null=True)
    read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)
    app_visibility = models.BooleanField(default=True)

class CronActions(models.Model):
    info = models.JSONField(default= dict)
    action = models.TextField()
    status = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)

TICKET_STATUS = (
    (1,"pending"),
    (2,"received"),
    (3,"closed"),
    (4,"replied"),
)

class UserAdminChat(models.Model):
    ticket_id = models.CharField(max_length=500,null=True,blank=True,unique=True)
    sender = models.ForeignKey(User,related_name='sender',on_delete=models.CASCADE,blank=True,null=True)
    receiver = models.ForeignKey(User,related_name='receiver',on_delete=models.CASCADE,blank=True,null=True)
    subject = models.CharField(max_length=250,null=True,blank=True)
    description = models.TextField(null=True,blank=True)
    comment = models.TextField(null=True,blank=True)
    status = models.PositiveIntegerField(choices=TICKET_STATUS,default=1)
    is_delete = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True,blank=True)
    updated_at = models.DateTimeField(blank=True,null=True)
    closing_date = models.DateTimeField(null=True,blank=True)

class UserMobile(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, blank=False, null=False)
    token = models.TextField()
    primary = models.CharField(max_length= 225)
    platform = models.CharField(max_length= 50)
    manufacturer = models.CharField(max_length= 100)
    model = models.CharField(max_length= 100)
    is_notify = models.BooleanField(default= True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)
    ip_address = models.CharField(max_length=100, null=True,blank=True)

class SalaryCertificatesRequests(models.Model):
    status_choices = (('pending','pending'),('approved','approved'),('rejected','rejected'),('deleted','deleted'))
    sender = models.ForeignKey(User,on_delete=models.CASCADE,related_name="sallary_certificates_sender",null=False,blank=False)
    # to = models.ForeignKey(User,on_delete=models.CASCADE,related_name="sallary_certificates_reciever",null=False,blank=False)
    to_name = models.TextField(null=True,blank=True)
    # designation = models.ForeignKey(Designation,on_delete=models.CASCADE,null=False,blank=False)
    month = models.CharField(max_length=50,null=False,blank=False)
    requested_date = models.DateTimeField(auto_now_add=True)
    approved_date = models.DateField(auto_now_add=False,null=True,blank=True)
    done_by = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True)
    status = models.CharField(max_length=20,null=False,blank=False,choices=status_choices,default='pending')
    salary = models.FloatField(null=True,blank=True)
    file = models.FileField(upload_to='salary_certificates',null=True,blank=True)
    employee_comment = models.TextField(null=True,blank=True)
    rejected_comment = models.TextField(null=True,blank=True)

@receiver(signal=post_delete,sender=SalaryCertificatesRequests)
def delete_salary_certifcate(sender,instance,*args,**kwargs):
    if instance.file:
        delete_files(instance.file.path)

class SalaryTransferLetter(models.Model):
    status_choices = (('pending','pending'),('approved','approved'),('rejected','rejected'),('deleted','deleted'))
    sender = models.ForeignKey(User,on_delete=models.CASCADE,related_name="sallary_transefer_letter_sender",null=False,blank=False)
    # to = models.ForeignKey(User,on_delete=models.CASCADE,related_name="sallary_transefer_letter_reciever",null=False,blank=False)
    to_name = models.TextField(null=True,blank=True)
    bank_name = models.CharField(max_length=120)
    branch_name = models.CharField(max_length=120)
    requested_date = models.DateTimeField(auto_now_add=True)
    approved_date = models.DateField(auto_now_add=False,null=True,blank=True)
    done_by = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True)
    status = models.CharField(max_length=20,null=False,blank=False,choices=status_choices,default='pending')
    salary = models.FloatField(null=True,blank=True)
    file = models.FileField(upload_to='salary_transfer_letter',null=True,blank=True)
    employee_comment = models.TextField(null=True,blank=True)
    rejected_comment = models.TextField(null=True,blank=True)

@receiver(signal=post_delete,sender=SalaryTransferLetter)
def delete_salary_transferletter(sender,instance,*args,**kwargs):
    if instance.file:
        delete_files(instance.file.path)

class ExperienceCertificate(models.Model):
    status_choices = (('pending','pending'),('approved','approved'),('rejected','rejected'),('deleted','deleted'))
    sender = models.ForeignKey(User,on_delete=models.CASCADE,related_name="experience_certificates_sender",null=False,blank=False)
    requested_date = models.DateTimeField(auto_now_add=True)
    approved_date = models.DateField(auto_now_add=False,null=True,blank=True)
    done_by = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True)
    status = models.CharField(max_length=20,null=False,blank=False,choices=status_choices,default='pending')
    file = models.FileField(upload_to='experience_certificates',null=True,blank=True)
    employee_comment = models.TextField(null=True,blank=True)
    rejected_comment = models.TextField(null=True,blank=True)

@receiver(signal=post_delete,sender=ExperienceCertificate)
def delete_experience_certificate(sender,instance,*args,**kwargs):
    if instance.file:
        delete_files(instance.file.path)

class NonObjectionCertificate(models.Model):
    status_choices = (('pending','pending'),('approved','approved'),('rejected','rejected'),('deleted','deleted'))
    purpose_choices = (('business_purpose','business_purpose'),('tourism_purpose','tourism_purpose'))
    designation_choices = (('as_per_visa','as_per_visa'),('as_per_profile','as_per_profile'))
    sender = models.ForeignKey(User,on_delete=models.CASCADE,related_name="nonobjection_certificate_sender",null=False,blank=False)
    # to = models.ForeignKey(User,on_delete=models.CASCADE,related_name="nonobjection_certificate_reciever",null=False,blank=False)
    purpose = models.CharField(max_length=100,null=True,blank=True,choices=purpose_choices)
    to_name = models.TextField(null=True,blank=True)
    placeoftravel = models.CharField(max_length=200,null=True,blank=True)
    travel_country = models.CharField(max_length=200,null=True,blank=True)
    visa_country = models.CharField(max_length=200,null=True,blank=True)
    requested_date = models.DateTimeField(auto_now_add=True)
    approved_date = models.DateField(auto_now_add=False,null=True,blank=True)
    done_by = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True)
    status = models.CharField(max_length=20,null=False,blank=False,choices=status_choices,default='pending')
    salary = models.PositiveBigIntegerField(null=True,blank=True)
    from_date = models.DateField(null=True,blank=True,auto_now=False)
    to_date = models.DateField(null=True,blank=True,auto_now=False)
    office_return_date = models.DateField(null=True,blank=True,auto_now=False)
    entry_visa = models.CharField(max_length=200,null=True,blank=True)
    designation = models.CharField(max_length=20,choices=designation_choices,default='as_per_profile')
    file = models.FileField(upload_to='noc_certificates',null=True,blank=True)
    employee_comment = models.TextField(null=True,blank=True)
    rejected_comment = models.TextField(null=True,blank=True)
    include_salary = models.BooleanField(default=False)
    include_education = models.BooleanField(default=False)
    education_details = models.CharField(max_length=200,null=True,blank=True)

@receiver(signal=post_delete,sender=NonObjectionCertificate)
def delete_noc_certificate(sender,instance,*args,**kwargs):
    if instance.file:
        delete_files(instance.file.path)

class DrivingLicensePermission(models.Model):
    status_choices = (('pending','pending'),('approved','approved'),('rejected','rejected'),('deleted','deleted'))
    sender = models.ForeignKey(User,on_delete=models.CASCADE,related_name="drivinglicense_permission",null=False,blank=False)
    requested_date = models.DateTimeField(auto_now_add=True)
    approved_date = models.DateField(auto_now_add=False,null=True,blank=True)
    done_by = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True)
    status = models.CharField(max_length=20,null=False,blank=False,choices=status_choices,default='pending')
    file = models.FileField(upload_to='driving_license_permission_certificates',null=True,blank=True)
    employee_comment = models.TextField(null=True,blank=True)
    rejected_comment = models.TextField(null=True,blank=True)

@receiver(signal=post_delete,sender=DrivingLicensePermission)
def delete_driving_license_permission(sender,instance,*args,**kwargs):
    if instance.file:
        delete_files(instance.file.path)   

CONTENT_TYPES = (
    (1,"Folder"),
    (2,"File"),
)


class PolicyProcedure(models.Model):
    name = models.CharField(max_length=250,null=True,blank=True)
    menu_type = models.PositiveIntegerField(choices=MENU_TYPES,null=True,blank=True)
    content_type = models.PositiveIntegerField(choices=CONTENT_TYPES,default=1)
    file = models.FileField(upload_to='policies',null=True,blank=True)
    parent = models.ForeignKey('self',on_delete=models.CASCADE,null=True,blank=True,related_name='parent_policy')
    extension = models.CharField(max_length=4,null=True,blank=True)
    added_by = models.ForeignKey(User,on_delete=models.CASCADE,null=False,blank=False)
    parent_list = models.TextField(null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

@receiver(signal=post_delete,sender=PolicyProcedure)
def delete_policy_documents(sender,instance,*args,**kwargs):
    if instance.file:
        delete_files(instance.file.path)
        
class TrainingCourse(models.Model):
    name = models.CharField(max_length=200, blank=False, null=False)
    description = models.TextField(blank=True, null=True)
    department = models.ForeignKey(Department,on_delete=models.CASCADE,null=False,blank=False)
    thumbnail = models.FileField(upload_to='course_nail',null=True,blank=True) 
    introduction_video = models.FileField(upload_to='course_intro',null=True,blank=True) 
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.name


FILE_TYPES = ((1, 'Video'), (2, 'Image'), (3, 'Document'), (4, 'Link'))
class CourseContent(models.Model):
    name = models.CharField(max_length=200, blank=False, null=False)
    course = models.ForeignKey(TrainingCourse, on_delete=models.CASCADE, null=True, blank=True, related_name='course_contents')
    external_link = models.CharField(max_length=200, blank=True, null=True)
    file = models.FileField(upload_to='course_files',null=True,blank=True)
    file_type = models.PositiveIntegerField(choices=FILE_TYPES, blank=True, null=True)
    duration = models.PositiveIntegerField(blank=True, null=True)
    sort_order = models.PositiveIntegerField(blank=True, null=True)
    is_intro = models.BooleanField(default=True,null=True,blank=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.name

COURSE_STATUS = (
    (1,"Pending"),
    (2,"On-going"),
    (3,"Completed"),
)
class CourseEmployees(models.Model):
    course = models.ForeignKey(TrainingCourse, on_delete=models.CASCADE,null=False, blank=False, related_name='related_courses')
    user = models.ForeignKey(User, on_delete=models.CASCADE,null=False, blank=False, related_name='users_on_course')
    status = models.PositiveIntegerField(choices=COURSE_STATUS, default=1, blank=True, null=True)    
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)


HISTORY_STATUS = ((1, 'Pending'),(2, 'On-going'), (3, 'Completed'))
class CourseContentHistory(models.Model):
    course_content = models.ForeignKey(CourseContent, on_delete=models.CASCADE,null=False, blank=False, related_name='related_course_history')
    user = models.ForeignKey(User, on_delete=models.CASCADE,null=False, blank=False, related_name='related_course_his_user')
    completed_duration = models.PositiveIntegerField(blank=True, null=True)
    status = models.PositiveIntegerField(choices=HISTORY_STATUS, default=1, blank=True, null=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

class MedicalInsuranceDocuments(models.Model):
    name = models.CharField(max_length=200, blank=False, null=False)
    file = models.FileField(upload_to='inusrance_docs',null=False,blank=False)
    insurance = models.ForeignKey(InusrancePolicy, on_delete=models.CASCADE,null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)


@receiver(signal=post_delete,sender=MedicalInsuranceDocuments)
def delete_insuarance_documents(sender,instance,*args,**kwargs):
    if instance.file:
        delete_files(instance.file.path)


class ExperienceYears(models.Model):
    name = models.CharField(max_length=200, blank=False, null=False)
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.name


class ExperienceLevel(models.Model):
    name = models.CharField(max_length=200, blank=False, null=False)
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.name
class CorporateVideo(models.Model):
    video_title = models.CharField(max_length=200, blank=False, null=False)
    file = models.FileField(upload_to='coorporate_video', blank=True, null=True)
    thumbnail = models.FileField(upload_to='coorporate_video_thumbnail', null=True, blank=True)
    size_or_length = models.CharField(max_length=200, blank=True, null=True)
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.video_title

class JobDescription(models.Model):
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True)
    job_description_title = models.CharField(max_length=200, blank=False, null=False)
    file = models.FileField(upload_to='job_description', blank=True, null=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='job_description', null=True, blank=True)
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.job_description_title


APPLICATION_METHOD = (
    (1, "Email"),
    (2, "Online")
)
class Careers(models.Model):
    post_name = models.CharField(max_length=200, blank=False, null=False)
    title_slug = AutoSlugField(populate_from='post_name', blank=True, null=True, unique=True)
    date = models.DateField(auto_now_add=False, blank=True, null=True)
    location = models.CharField(max_length=200, blank=False, null=False)
    job_types = models.ForeignKey(JobTypes, on_delete=models.CASCADE, null=True, blank=True)
    experience_years = models.ForeignKey(ExperienceYears, on_delete=models.CASCADE, null=True, blank=True)
    experience_level = models.ForeignKey(ExperienceLevel, on_delete=models.CASCADE, null=True, blank=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True)
    description = models.CharField(max_length=200, blank=False, null=False)
    to_email = models.EmailField(max_length=255, blank=True, null=True)
    job_summary = models.TextField(blank=False, null=False)
    responsibilities_and_duties = models.TextField(blank=False, null=False)
    application_method = models.PositiveIntegerField(choices=APPLICATION_METHOD, default=1)
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True)




class HolidayLeaveAdjustments(models.Model):
    user =  models.ForeignKey(User, on_delete=models.CASCADE, related_name='holiday_leave')
    holiday =  models.ForeignKey(Holidays, on_delete=models.CASCADE, related_name='leave_adj_holiday', blank=True, null=True)
    leave =  models.ForeignKey(LeaveRequest, on_delete=models.CASCADE, related_name='holiday_leave')
    new_count = models.FloatField(null=True, blank=True)
    leave_count_before =  models.FloatField(null=True, blank=True)
    leave_count_after =  models.FloatField(null=True, blank=True)
    leave_status = models.IntegerField(null=True,blank=True)
    reason = models.TextField(blank=True, null=True)
    added_by = models.ForeignKey(User,on_delete=models.CASCADE, related_name='change_added_by')
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)

APPLICATION_STATUS =((1, 'Pending'),(2, 'Interviewed'),(3, 'Selected'),(4, 'Rejected'))

class JobApplication(models.Model):
    first_name = models.CharField(max_length=200, blank=False, null=False)
    last_name = models.CharField(max_length=200, blank=False, null=False)
    email = models.EmailField(verbose_name='email address', max_length=255)
    contact_number = PossiblePhoneNumberField(blank=True, default="", db_index=True)
    job_title = models.CharField(max_length=200, blank=False, null=False)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True)
    job_types = models.ForeignKey(JobTypes, on_delete=models.CASCADE, null=True, blank=True)
    experience_years = models.ForeignKey(ExperienceYears, on_delete=models.CASCADE, null=True, blank=True)
    experience_level = models.ForeignKey(ExperienceLevel, on_delete=models.CASCADE, null=True, blank=True)
    location = models.CharField(max_length=200, blank=False, null=False)
    cover_letter = models.TextField(blank=False, null=False)
    file = models.FileField(upload_to='job_application', blank=True, null=True)
    status = models.PositiveIntegerField(choices=APPLICATION_STATUS, default=1, blank=True, null=True)
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.first_name


class Events(models.Model):
    created_at = models.DateField(auto_now_add=True)
    event_date = models.DateTimeField(auto_now_add=False,null=True,blank=True)
    name = models.TextField()
    created_by = models.ForeignKey(User,null=True,blank=True,on_delete=models.CASCADE)
    class Meta:
        unique_together = ('event_date','name')


def events_file(instance, filename):
    instance_id = GetLastPK(instance)
    ext = filename.split(".")[-1]
    newname = "events/file" + str( instance_id + 1) + "." + ext
    return newname  


class EventFiles(models.Model):
    file_type_choices = (('image','image'),('video','video'))
    event = models.ForeignKey(Events,on_delete=models.CASCADE,related_name='events_files')
    title = models.TextField(null=True,blank=True)
    file = models.FileField(upload_to=events_file)
    file_type = models.CharField(max_length=100,choices=file_type_choices)

@receiver(signal=post_delete,sender=EventFiles)
def delete_events_files(sender,instance,*args,**kwargs):
    if instance.file:
        delete_files(instance.file.path)



class TrainingRequests(models.Model):
    user =  models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_training_request')
    subject =  models.CharField(max_length=250, blank=True, null=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    

REQUEST_STATUS =((1, 'Pending'),(2, 'Cancelled'),(3, 'Approved'),(4, 'Rejected'))
class TrainingRequestStatus(models.Model):
    request =  models.ForeignKey(TrainingRequests, on_delete=models.CASCADE, related_name='training_requests')
    added_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='request_action_done_by')
    status = models.PositiveIntegerField(choices=REQUEST_STATUS, default=1, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)

class FlightTicket(models.Model):
    status_choices = (('pending','pending'),('approved','approved'),('rejected','rejected'),('deleted','deleted'),("in progress","in progress"))
    type_choices = (('One Way','One Way'),('Two Way','Two Way'))
    requested_date = models.DateTimeField(auto_now_add=True)
    sender = models.ForeignKey(User,on_delete=models.CASCADE,related_name="flightticket_requests_sender",null=False,blank=False)
    type = models.CharField(choices=type_choices,max_length=50,null=True,blank=True)
    airline = models.TextField()
    deperature_date = models.DateField()
    return_date = models.DateField()
    no_of_days = models.IntegerField()
    country_of_departure = models.CharField(max_length=200,null=True,blank=True)
    city_of_deperature = models.CharField(max_length=150,null=True,blank=True)
    country_of_arrival = models.CharField(max_length=200,null=True,blank=True)
    city_of_arrival = models.CharField(max_length=200,null=True,blank=True)
    done_by = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True)
    approved_date = models.DateField(auto_now_add=False,null=True,blank=True)
    status = models.CharField(max_length=20,null=False,blank=False,choices=status_choices,default='pending')
    employee_comment = models.TextField(null=True,blank=True)
    rejected_comment = models.TextField(null=True,blank=True)
    remaining_flight_ticket = models.FloatField(null=True,blank=True)
    remark = models.TextField(null=True,blank=True)
    leave_request = models.ForeignKey(LeaveRequest,on_delete=models.CASCADE,related_name='ticket_leave_request',null=True,blank=True)
    extra_ticket = models.IntegerField(default=1)

    class Meta:
        unique_together = ('sender','airline','deperature_date','return_date','city_of_deperature','country_of_arrival','city_of_arrival','status')

def stamp_file(instance, filename):
    instance_id = GetLastPK(instance)
    ext = filename.split(".")[-1]
    newname = "stamp/file" + str( instance_id + 1) + "." + ext
    return newname  

def signature_file(instance, filename):
    instance_id = GetLastPK(instance)
    ext = filename.split(".")[-1]
    newname = "signature/file" + str( instance_id + 1 ) + "." + ext
    return newname  

class StampAndSignature(models.Model):
    user = models.OneToOneField(User,on_delete=models.CASCADE)
    stamp = models.FileField(upload_to=stamp_file)
    signature = models.FileField(upload_to=signature_file)

class AttendanceLog(models.Model):
    emp_id = models.CharField(max_length=150,null=True,blank=True)
    access_date_time = models.DateTimeField(null=True,blank=True)
    access_date = models.DateField(null=True,blank=True)
    access_time = models.TimeField(null=True,blank=True)
    authentication_result = models.CharField(max_length=150,null=True,blank=True)
    authentication_succeeded = models.CharField(max_length=150,null=True,blank=True)
    authentication_failed = models.CharField(max_length=150,null=True,blank=True)
    authentication_type = models.CharField(max_length=150,null=True,blank=True)
    device_name = models.CharField(max_length=250,null=True,blank=True)
    device_serial_number = models.CharField(max_length=150,null=True,blank=True)
    reader_name = models.CharField(max_length=150,null=True,blank=True)
    first_name = models.CharField(max_length=150,null=True,blank=True)
    last_name = models.CharField(max_length=150,null=True,blank=True)
    person_name = models.CharField(max_length=150,null=True,blank=True)
    department = models.CharField(max_length=150,null=True,blank=True)
    card_number = models.CharField(max_length=150,null=True,blank=True)
    direction = models.CharField(max_length=150,null=True,blank=True)
    direction_enter = models.CharField(max_length=150,null=True,blank=True)
    direction_exit = models.CharField(max_length=150,null=True,blank=True)
    skin_surface_temperature = models.CharField(max_length=150,null=True,blank=True)
    normal_temperature = models.CharField(max_length=150,null=True,blank=True)
    temperature_status = models.CharField(max_length=150,null=True,blank=True)
    abnormal_temperature = models.CharField(max_length=150,null=True,blank=True)
    temperature_unknown = models.CharField(max_length=150,null=True,blank=True)
    wearing_mask = models.CharField(max_length=150,null=True,blank=True)
    with_mask = models.CharField(max_length=150,null=True,blank=True)
    no_mask = models.CharField(max_length=150,null=True,blank=True)
    mask_unknown = models.CharField(max_length=150,null=True,blank=True)
    info = models.JSONField(default=dict,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    
class AttendanceRequests(models.Model):
    STATUS_CHOICES = (('pending','pending'),('approved','approved'),('rejected','rejected'))
    sender = models.ForeignKey(User,on_delete=models.CASCADE,related_name='requests_sender')
    attendance = models.ForeignKey(AttendanceLog,on_delete=models.CASCADE,null=True,blank=True)
    reason = models.TextField(null=True,blank=True)
    explanation = models.TextField(null=True,blank=True)
    approvedor_rejectedby = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True,related_name='requests_doneby')
    status = models.CharField(max_length=50,choices=STATUS_CHOICES,default='pending')
    requested_date = models.DateTimeField(null=True,blank=True)
    attendance_date = models.DateField(null=True,blank=True)
    rejected_command = models.TextField(null=True,blank=True)
    actual_punchin = models.TimeField(null=True,blank=True)
    actual_punchout = models.TimeField(null=True,blank=True)
    corrected_punchin = models.TimeField(null=True,blank=True)
    corrected_punchout = models.TimeField(null=True,blank=True)

PAYROLL_UPLOAD_STATUS =((1, 'Pending'),(2, 'Processed'),(3, 'Rollbacked'),(4, 'Deleted'))

class PayrollUpload(models.Model):
    file = models.FileField(upload_to='payroll_xls', null=True, blank=True)
    month = models.PositiveIntegerField()
    year = models.PositiveIntegerField()
    status = models.PositiveIntegerField(choices=PAYROLL_UPLOAD_STATUS, default=1, blank=True, null=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='payroll_uploaded')
    key_values = models.JSONField(default=dict, blank=True, null=True)
    error_logs = models.JSONField(default=dict, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

PAYROLL_DIVISION_TYPE =((1, 'Credit'),(2, 'Debit'))
class PayrollDivision(models.Model):
    title = models.CharField(max_length=150, blank=False, null=False)
    type = models.PositiveIntegerField(choices=PAYROLL_DIVISION_TYPE, default=1, blank=True, null=True)
    sort_order = models.PositiveIntegerField(blank=True, null=True)
    is_active = models.BooleanField(default=True, null=True, blank=True)
    in_effect_month = models.PositiveIntegerField()
    in_effect_year = models.PositiveIntegerField()
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)


class PayrollMaster(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, blank=False, null=False, related_name='payroll_user')
    name_of_the_employee = models.CharField(max_length=150,null=True,blank=True)
    payroll_upload = models.ForeignKey(PayrollUpload, on_delete=models.CASCADE, blank=False, null=False, related_name='payroll_upload')
    month = models.PositiveIntegerField()
    year = models.PositiveIntegerField()
    present_days = models.FloatField(null=True, blank=True)
    total_days = models.FloatField(null=True, blank=True)
    sick_leaves = models.FloatField(null=True, blank=True, default=0)
    absent_days = models.FloatField(null=True, blank=True)
    rate_per_day = models.FloatField(null=True, blank=True)
    late_comings = models.FloatField(null=True, blank=True, default=0)
    paid_by = models.CharField(max_length=50,null=True,blank=True)
    total_salary_to_be_paid = models.FloatField(null=True, blank=True)
    total_salary = models.FloatField(null=True, blank=True)
    gross_salary = models.FloatField(null=True, blank=True)
    deduction = models.FloatField(null=True, blank=True)
    month_and_year = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

class PayrollDetail(models.Model):
    payroll_master = models.ForeignKey(PayrollMaster, on_delete=models.CASCADE, blank=False, null=False, related_name='payroll_details')
    payroll_division = models.ForeignKey(PayrollDivision, on_delete=models.CASCADE, blank=False, null=False, related_name='payroll_divisions')
    amount = models.FloatField(null=True, blank=True)
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)


class RelatedCourse(models.Model):
    course = models.ForeignKey(TrainingCourse, on_delete=models.CASCADE, null=True, blank=True, related_name='related_course_of')
    related_course = models.ForeignKey(TrainingCourse, on_delete=models.CASCADE, null=True, blank=True, related_name='course_related')
    sort_order = models.PositiveIntegerField(blank=True, null=True)
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

CREATE, READ, UPDATE, DELETE = "Create", "Read", "Update", "Delete"
LOGIN, LOGOUT, LOGIN_FAILED = "Login", "Logout", "Login Failed"
ACTION_TYPES = [
    (CREATE, CREATE),
    (READ, READ),
    (UPDATE, UPDATE),
    (DELETE, DELETE),
    (LOGIN, LOGIN),
    (LOGOUT, LOGOUT),
    (LOGIN_FAILED, LOGIN_FAILED),
]

SUCCESS, FAILED = "Success", "Failed"
ACTION_STATUS = [(SUCCESS, SUCCESS), (FAILED, FAILED)]


class ActivityLog(models.Model):
    actor = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    action_type = models.CharField(choices=ACTION_TYPES, max_length=15)
    action_time = models.DateTimeField(auto_now_add=True)
    remarks = models.TextField(blank=True, null=True)
    status = models.CharField(choices=ACTION_STATUS, max_length=7, default=SUCCESS)
    data = models.JSONField(default=dict)
    app_visibility = models.BooleanField(default=False)
    web_visibility = models.BooleanField(default=False)
    activity_mode = models.CharField(max_length=10,null=True,blank=True,default='Web')
    module_name = models.CharField(max_length=50,null=True,blank=True)
    error_msg = models.TextField(blank=True, null=True)
    path_info = models.JSONField(default=dict)
    fwd_link = models.CharField(max_length=100,null=True,blank=True)

    # for generic relations
    content_type = models.ForeignKey(
        ContentType, models.SET_NULL, blank=True, null=True
    )
    object_id = models.PositiveIntegerField(blank=True, null=True)
    content_object = GenericForeignKey()

    def __str__(self) -> str:
        return f"{self.action_type} by {self.actor} on {self.action_time}"

class ProbationPoint(models.Model):
    name = models.CharField(max_length=250,null=False,blank=False)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class ProbationRating(models.Model):
    name = models.CharField(max_length=250,null=False,blank=False)
    value = models.IntegerField(blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class ProbationRatingComments(models.Model):
    probation_point = models.ForeignKey(ProbationPoint,on_delete=models.CASCADE, related_name='point_comments')
    probation_rate = models.ForeignKey(ProbationRating,on_delete=models.CASCADE, related_name='rate_comments')    
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

PROBATION_STATUS = ((1, 'Pending'),(2, 'Waiting for HOD approval'),
                    (3, 'Waiting for employee approval'),(4, 'Waiting for HR approval'),
                    (5, 'Waiting for MD approval'),(6, 'MD approved'),
                    (7, 'Rejected'),
                    )

APPROVED,PENDING,REJECTED,IN_PROGRESS = "approved", "pending", "rejected", "in progress"
APPROVAL_STATUS = [
    (1, PENDING),
    (2, APPROVED),
    (3, REJECTED),
    (4, IN_PROGRESS),
]
class ProbationForm(models.Model):
    user = models.ForeignKey(User,on_delete=models.CASCADE, related_name='user_probation_forms')
    assigned_to = models.ForeignKey(User,on_delete=models.CASCADE, related_name='user_probation_assigner')
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    status = models.PositiveIntegerField(choices=PROBATION_STATUS, null=True, blank=True,default=1)
    overall_rating = models.CharField(max_length=200,blank=True, null=True)
    summary = models.TextField(blank=True, null=True) 
    action_plan = models.TextField(blank=True, null=True) 
    is_permanent = models.BooleanField(default=False)
    is_dismissed = models.BooleanField(default=False)
    is_extended = models.BooleanField(default=False)   
    month_extended = models.IntegerField(default=0)   
    supervisor_signature = models.ImageField(upload_to="probation_signature",null=True,blank=True)
    supervisor_signature_date = models.DateField(null=True,blank=True)
    employee_signature = models.ImageField(upload_to="probation_signature",null=True,blank=True)
    employee_signature_date = models.DateField(null=True,blank=True)
    hr_signature = models.ImageField(upload_to="probation_signature",null=True,blank=True)
    hr_signature_date = models.DateField(null=True,blank=True)
    md_signature = models.ImageField(upload_to="probation_signature",null=True,blank=True)
    md_signature_date = models.DateField(null=True,blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)
    probation_status = models.PositiveIntegerField(choices=APPROVAL_STATUS, null=True, blank=True,default=1)

class ProbationEvaluation(models.Model):
    probation_form = models.ForeignKey(ProbationForm,on_delete=models.CASCADE, related_name='user_probation_evals')
    probation_point = models.ForeignKey(ProbationPoint,on_delete=models.CASCADE, related_name='user_probation_points')
    probation_rate = models.ForeignKey(ProbationRating,on_delete=models.CASCADE, related_name='user_probation_rates')
    probation_comment = models.TextField(blank=True, null=True) 
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)



class ProbationStatusLog(models.Model):
    probation_form = models.ForeignKey(ProbationForm,on_delete=models.CASCADE, default=None, related_name = "probation_form_statuses")
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    status = models.PositiveIntegerField(choices=PROBATION_STATUS, null=True, blank=True,default=1)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)


class SplashScreen(models.Model):
    title = models.CharField(max_length=1000,null=True,blank=True)
    title2 = models.CharField(max_length=1000,null=True,blank=True)
    description = models.TextField(null=True,blank=True)
    splash_screen_img = models.FileField(upload_to='splash_img',null=True,blank=True)
    button_name = models.CharField(max_length=500,null=True,blank=True)
    order = models.PositiveIntegerField(null=True,blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)


def ticket_img_rename(instance,filename):
    instance_id = GetLastPK(instance)
    ext = filename.split(".")[-1]
    newname = "ticket_img/" + str( instance_id + 1 ) + "." + ext
    return newname

class TicketFileUpload(models.Model):
    ticket_img = models.FileField(upload_to=ticket_img_rename,null=True,blank=True)
    ticket = models.ForeignKey(UserAdminChat,on_delete=models.CASCADE,null=True,blank=True,related_name="ticket_file_upload")
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    is_active = models.BooleanField(default=True)


class WorkStatusLog(models.Model):
    created_at = models.DateField(auto_now_add=True)
    user = models.ForeignKey(User,on_delete=models.CASCADE,related_name='user_workstatus_log')
    status = models.ForeignKey(UserStatus,on_delete=models.CASCADE)

class AttendanceRequestsLog(models.Model):
    crated_at = models.DateTimeField(auto_now_add=True)
    attendance = models.ForeignKey(AttendanceRequests,on_delete=models.CASCADE,related_name='attendance_requestslog')
    punchin = models.TimeField(null=True,blank=True)
    punchout = models.TimeField(null=True,blank=True)
    comment = models.TextField(null=True,blank=True)


# class AttendanceGraphLog(models.Model):
#     year = models.IntegerField(default=None,null=True, blank=True)
#     month = models.IntegerField(default=None,null=True, blank=True)
#     total_no_days_in_month = models.IntegerField(default=0,null=True, blank=True)
#     total_working_days_of_month = models.IntegerField(default=0,null=True, blank=True)
#     total_no_of_employees = models.IntegerField(default=0,null=True, blank=True)
#     absent_percentage = models.FloatField(default=0, null=True, blank=True)
#     present_percentage = models.FloatField(default=0, null=True, blank=True)

class AttendanceGraphLogEmployee(models.Model):
    emp_id = models.CharField(max_length=150, null=True, blank=True)
    year = models.IntegerField(default=None,null=True, blank=True)
    month = models.IntegerField(default=None,null=True, blank=True)
    total_no_days_in_month = models.IntegerField(default=0,null=True, blank=True)
    total_working_days_of_month = models.IntegerField(default=0,null=True, blank=True)
    total_present_days = models.FloatField(default=0, null=True, blank=True)
    total_absent_days = models.FloatField(default=0, null=True, blank=True)
    absent_percentage = models.FloatField(default=0, null=True, blank=True)
    present_percentage = models.FloatField(default=0, null=True, blank=True)
    
class VisaType(models.Model):
    name = models.CharField(max_length=100,null=True,blank=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)

IMPORT,EXPORT = "Import", "Export"
ACTION_TYPES = [
    (IMPORT, IMPORT),
    (EXPORT, EXPORT)
]

SUCCESS, FAILED = "Success", "Failed"
ACTION_STATUS = [(SUCCESS, SUCCESS), (FAILED, FAILED)]

class KPIExportLog(models.Model):
    file_name = models.CharField(max_length=200,null=True,blank=True)
    actor = models.ForeignKey(User, on_delete=models.CASCADE, null=True, related_name='kpi_loguser')
    kpi_timeperiod = models.ForeignKey(KpiTimePeriod, on_delete=models.CASCADE, null=True, related_name='kpi_timeperiod')
    info = models.JSONField(default=dict)
    action_time = models.DateTimeField(auto_now_add=True)
    action_type = models.CharField(choices=ACTION_TYPES, max_length=15)
    status = models.CharField(choices=ACTION_STATUS, max_length=7, default=SUCCESS)

    def __str__(self) -> str:
        return f"{self.action_type} by {self.actor} on {self.action_time}"

KPI,APPRAISAL = "kpi", "appraisal"
ACTION_TYPES = [
    (KPI, KPI),
    (APPRAISAL, APPRAISAL)
]   
class KPILevels(models.Model):
    name = models.CharField(max_length=200,null=True,blank=True)
    order = models.IntegerField(default=None,null=True, blank=True)
    level_key = models.CharField(max_length=100,blank=True,null=True)
    action_key = models.CharField(choices=ACTION_TYPES,max_length=15)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)

    def __str__(self) -> str:
        return f"{self.name}"

KPI,GOAL = "kpi", "goal"
ACTION_TYPES = [
    (KPI, KPI),
    (GOAL, GOAL)
]   
class KPILevelPermissions(models.Model):
    kpilevel_id = models.ForeignKey(KPILevels,on_delete=models.CASCADE,related_name='kpi_levels')
    user_id = models.ForeignKey(User,on_delete=models.CASCADE,related_name='kpilevel_user')
    assigned_to = models.ForeignKey(User,on_delete=models.CASCADE,null=False,blank=False,related_name="kpilevel_assigned")
    action_type = models.CharField(choices=ACTION_TYPES, max_length=15,null=True,blank=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)

APPROVED,PENDING,REJECTED = "approved", "pending", "rejected"
KPI_STATUS = [
    (APPROVED, APPROVED),
    (PENDING, PENDING),
    (REJECTED, REJECTED)
]

KPI,GOAL = "kpi", "goal"
ACTION_TYPES = [
    (KPI, KPI),
    (GOAL, GOAL)
]   
class KPIApprovalStatus(models.Model):
    kpiuser_timeperiod = models.ForeignKey(KpiUserTimePeriod,on_delete=models.CASCADE,related_name='kpiuser_timeperiod',null=True,blank=True)
    kpilevel_id = models.ForeignKey(KPILevels,on_delete=models.CASCADE,related_name='kpiapprove_levels',null=True,blank=True)
    added_by = models.ForeignKey(User,on_delete=models.CASCADE,related_name='approval_assignedby',null=True)
    approved_rejected_user = models.ForeignKey(User,on_delete=models.CASCADE,related_name='approved_rejected_users',null=True,blank=True)
    rejected_to = models.ForeignKey(User,on_delete=models.CASCADE,related_name='rejected_to',null=True,blank=True,default=None)
    kpi_user_year = models.ForeignKey(KpiUserYear,on_delete=models.CASCADE,related_name='approval_user_year',blank=True,null=True)
    is_approve = models.BooleanField(default=False)
    remarks = models.TextField(blank=True, null=True)
    approval_status = models.CharField(choices=KPI_STATUS, max_length=30, default=PENDING)
    action_type = models.CharField(choices=ACTION_TYPES, max_length=15,null=True,blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

APPROVED,PENDING,REJECTED = "approved", "pending", "rejected"
KPI_STATUS = [
    (APPROVED, APPROVED),
    (PENDING, PENDING),
    (REJECTED, REJECTED)
]
class KPIRemarks(models.Model):
    kpi_approvalstatus = models.ForeignKey(KPIApprovalStatus,on_delete=models.CASCADE,related_name='kpi_approvalstatus',null=True,blank=True)
    kpi_added_by = models.ForeignKey(User,on_delete=models.CASCADE,related_name='kpi_added_by',null=True)
    approve_reject_user = models.ForeignKey(User,on_delete=models.CASCADE,related_name='approve_reject_users',null=True,blank=True)
    approval_status = models.CharField(choices=KPI_STATUS, max_length=30, default=PENDING)
    remarks = models.TextField(blank=True, null=True,default=None)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)

class KPIUserGoalValues(models.Model):
    kpiuser_timeperiod = models.ForeignKey(KpiUserTimePeriod,on_delete=models.CASCADE,related_name='kpiusergoal_timeperiod',null=True,blank=True)
    kpiuser_goal = models.ForeignKey(KpiUserGoal,on_delete=models.CASCADE,related_name="kpiuser_goals")    
    goal_value = models.TextField(null=True,blank=True)
    max_finish_rate = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    weight = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    actual = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    target = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    remark = models.TextField(null=True,blank=True)
    in_percentage = models.BooleanField(default=True, help_text="Indicates if actual and target values should be displayed with % symbol")

ACTION_TYPES = [
    (1, "kpi"),
    (2, "goal")
] 

class ApprovalMessages(models.Model):
    message = models.TextField(null=True,blank=True)
    sender = models.ForeignKey(User,on_delete=models.CASCADE,related_name='approval_sender')
    receiver = models.CharField(max_length=50,null=True)
    kpi_user = models.ForeignKey(User,on_delete=models.CASCADE,related_name='kpi_users')
    user_timeperiod = models.ForeignKey(KpiUserTimePeriod,on_delete=models.CASCADE,related_name='approval_timeperiod',null=True,blank=True)
    user_year = models.ForeignKey(KpiUserYear,on_delete=models.CASCADE,related_name='approval_useryear',blank=True,null=True)
    action_type = models.PositiveIntegerField(choices=ACTION_TYPES)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)

# probation 
PROBATION_ACTION_TYPE = (
    (1,"Probation Point"),
    (2,"Probation Creation"),
)

class ProbationAction(models.Model):
    name = models.CharField(max_length=100,null=False,blank=False)
    probation_action_type = models.PositiveIntegerField(choices=PROBATION_ACTION_TYPE,default=1)
    is_active = models.BooleanField(default=True)
    key_action = models.CharField(max_length=100,blank=True,null=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class ProbationActionPermission(models.Model):
    probation_action = models.ForeignKey(ProbationAction,on_delete=models.CASCADE,null=True,blank=True,related_name="probationaction")
    assigned_to = models.ForeignKey(User,on_delete=models.CASCADE,null=False,blank=False,related_name="assigned_to_probation")
    user = models.ForeignKey(User,on_delete=models.CASCADE,null=True,blank=True,related_name="user_probation_permissions")
    department = models.ForeignKey(Department,on_delete=models.CASCADE,null=True,blank=True,related_name="department_probation_permissions")
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class ProbationLevels(models.Model):
    name = models.CharField(max_length=200,null=True,blank=True)
    order = models.IntegerField(default=None,null=True, blank=True)
    level_key = models.CharField(max_length=100,blank=True,null=True)
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

    def __str__(self) -> str:
        return f"{self.name}"
    
class ProbationLevelPermissions(models.Model):
    probation_level = models.ForeignKey(ProbationLevels,on_delete=models.CASCADE,related_name='probation_levels')
    user_id = models.ForeignKey(User,on_delete=models.CASCADE,related_name='probationlevel_user')
    assigned_to = models.ForeignKey(User,on_delete=models.CASCADE,null=False,blank=False,related_name="probationlevel_assigned")
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

APPROVED,PENDING,REJECTED = "approved", "pending", "rejected"
PROBATION_STATUS = [
    (APPROVED, APPROVED),
    (PENDING, PENDING),
    (REJECTED, REJECTED)
]
class ProbationApprovalStatus(models.Model):
    probation_form = models.ForeignKey(ProbationForm,on_delete=models.CASCADE,related_name='probation_approval',null=True,blank=True)
    probation_level = models.ForeignKey(ProbationLevels,on_delete=models.CASCADE,related_name='probation_approval_levels',null=True,blank=True)
    added_by = models.ForeignKey(User,on_delete=models.CASCADE,related_name='probation_assignedby',null=True)
    user = models.ForeignKey(User,on_delete=models.CASCADE,related_name='probation_users',null=True,blank=True)
    rejected_to = models.ForeignKey(User,on_delete=models.CASCADE,related_name='probation_rejected_to',null=True,blank=True,default=None)
    is_approve = models.BooleanField(default=False)
    remarks = models.TextField(blank=True, null=True)
    approval_status = models.CharField(choices=PROBATION_STATUS, max_length=30, default=PENDING)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

APPROVED,PENDING,REJECTED = "approved", "pending", "rejected"
PROBATION_STATUS = [
    (APPROVED, APPROVED),
    (PENDING, PENDING),
    (REJECTED, REJECTED)
]
class ProbationRemarks(models.Model):
    probation_approvalstatus = models.ForeignKey(ProbationApprovalStatus,on_delete=models.CASCADE,related_name='probation_approvalstatus',null=True,blank=True)
    probation_added_by = models.ForeignKey(User,on_delete=models.CASCADE,related_name='probation_added_by',null=True)
    user = models.ForeignKey(User,on_delete=models.CASCADE,related_name='probation_user_remark',null=True,blank=True)
    approval_status = models.CharField(choices=PROBATION_STATUS, max_length=30, default=PENDING)
    remarks = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class FlightTicketAdjustment(models.Model):
    assigned_to = models.ForeignKey(User,on_delete=models.CASCADE, related_name='ticket_adjustment_for')
    added_by = models.ForeignKey(User,on_delete=models.CASCADE, related_name='ticket_adjustment_added_by')
    adjustment_type = models.PositiveIntegerField(choices=ADJUSTMENT_TYPE, default=1)
    ticket_count = models.FloatField(null=True, blank=True)
    adjusted_date = models.DateTimeField(null=True, blank=True)
    comment = models.TextField(null=True, blank=True)
    opening_balance = models.PositiveIntegerField(blank=True, null=True)
    availed_ticket = models.PositiveIntegerField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)


class FlightTicketLevel(models.Model):
    name = models.CharField(max_length=250, null=False, blank=False)
    key_name = models.CharField(max_length=250,null=True,blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.name


class FlightTicketPermission(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='user_ticket_permission')
    ticket_level = models.ForeignKey(LeaveLevels, on_delete=models.CASCADE, default=None, related_name='ticket_permission_level')
    assigned_to = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='assigned_ticket_permission')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

APPROVED,PENDING,REJECTED = "approved", "pending", "rejected"
STATUS_CHOICE = [
    (APPROVED, APPROVED),
    (PENDING, PENDING),
    (REJECTED, REJECTED)
]
class FlightTicketStatusLog(models.Model):
    ticket_request = models.ForeignKey(FlightTicket, on_delete=models.CASCADE, null=True, blank=True, related_name='flight_ticket_request_log')
    ticket_levels = models.ForeignKey(FlightTicketLevel, on_delete=models.CASCADE,related_name='flight_ticket_level', null=True, blank=True)
    is_approve = models.BooleanField(default=False,null=True,blank=True,)
    remark = models.TextField(blank=True, null=True)
    user = models.ForeignKey(User,on_delete=models.CASCADE,related_name='flight_ticket_request_log_user',null=True,blank=True)
    added_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,related_name='ticket_log_user')
    is_active = models.BooleanField(default=True)
    status =  models.CharField(choices=STATUS_CHOICE, max_length=30, default=PENDING)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)


class AppraisalLevelPermissions(models.Model):
    appraisal_level = models.ForeignKey(KPILevels,on_delete=models.CASCADE,related_name='appraisal_levels')
    appraisal_user = models.ForeignKey(User,on_delete=models.CASCADE,related_name='appraisal_user')
    assigned_to = models.ForeignKey(User,on_delete=models.CASCADE,null=False,blank=False,related_name="appeaisal_level_assigned")
    is_active = models.BooleanField(default=True,null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

APPROVED,PENDING,REJECTED = "approved", "pending", "rejected"
APPRAISAL_APPROVAL_STATUS = [
    (APPROVED, APPROVED),
    (PENDING, PENDING),
    (REJECTED, REJECTED)
]

class AppraisalApprovalStatus(models.Model):
    user_appraisal = models.ForeignKey(UserAppraisalForms,on_delete=models.CASCADE,related_name='users_appraisal',null=True,blank=True)
    appraisal_level = models.ForeignKey(KPILevels,on_delete=models.CASCADE,related_name='appraisal_approval',null=True,blank=True)
    added_by = models.ForeignKey(User,on_delete=models.CASCADE,related_name='appraisal_addedby',null=True)
    user = models.ForeignKey(User,on_delete=models.CASCADE,related_name='appraisal_users',null=True,blank=True)
    rejected_to = models.ForeignKey(User,on_delete=models.CASCADE,related_name='appraisal_rejected_to',null=True,blank=True,default=None)
    appraisal_user_year = models.ForeignKey(KpiUserYear,on_delete=models.CASCADE,related_name='appraisal_users_year',blank=True,null=True)
    is_approve = models.BooleanField(default=False)
    remarks = models.TextField(blank=True, null=True)
    approval_status = models.CharField(choices=APPRAISAL_APPROVAL_STATUS, max_length=30, default=PENDING)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

class AppraisalRemarks(models.Model):
    appraisal_approvals = models.ForeignKey(AppraisalApprovalStatus,on_delete=models.CASCADE,related_name='appraisal_approvalstatus',null=True,blank=True)
    added_by = models.ForeignKey(User,on_delete=models.CASCADE,related_name='appraisal_added_by',null=True)
    user = models.ForeignKey(User,on_delete=models.CASCADE,related_name='appraisals_users',null=True,blank=True)
    approval_status = models.CharField(choices=APPRAISAL_APPROVAL_STATUS, max_length=30, default=PENDING)
    remarks = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

def quotation_file(instance, filename):
    instance_id = GetLastPK(instance)
    ext = filename.split(".")[-1]
    newname = "quotation/file" + str( instance_id + 1 ) + "." + ext
    return newname  

FILE_TYPE = ((1, 'Quotation'), (2, 'Ticket'))

class FlightTicketAttachments(models.Model):
    flight_ticket = models.ForeignKey(FlightTicket,on_delete=models.CASCADE,blank=True,null=True,related_name='flight_quotation')
    added_by = models.ForeignKey(User,on_delete=models.CASCADE,blank=True,null=True,related_name='quotation_addedby')
    user = models.ForeignKey(User,on_delete=models.CASCADE,blank=True,null=True,related_name='flight_ticket_user')
    title = models.TextField(null=True,blank=True)
    file = models.FileField(upload_to="flight_ticket_attachments")
    file_type = models.CharField(choices=FILE_TYPE,max_length=25)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)

VALUE_TYPE = (
    (1,"Criticism"),
    (2,"Appreciation"),
)

def performance_attachment_rename(instance,filename):
    instance_id = GetLastPK(instance)
    ext = filename.split(".")[-1]
    newname = "performance_letter/" + str( instance_id + 1 ) + "." + ext
    return newname

class PerformanceModificationLog(models.Model):
    kpi_user_timeperiod = models.ForeignKey(KpiUserTimePeriod,on_delete=models.CASCADE,blank=True,null=True,related_name='kpi_performance')
    previous_score = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    updated_score = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    value_type = models.PositiveIntegerField(choices=VALUE_TYPE)
    value = models.DecimalField(max_digits=30,decimal_places=2,null=True,blank=True)
    comments = models.TextField(blank=True, null=True)
    added_by = models.ForeignKey(User,on_delete=models.CASCADE,blank=True,null=True,related_name='performance_addedby')
    documents = models.FileField(upload_to=performance_attachment_rename,null=True,blank=True)
    modification_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateTimeField(auto_now=True,null=True,blank=True)
# Generated by Django 4.1.3 on 2025-07-30 09:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hisensehr', '0264_alter_kpiusergoal_goal_value_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='kpiuseractual',
            name='in_percentage',
            field=models.BooleanField(default=True, help_text='Indicates if actual value should be displayed with % symbol'),
        ),
        migrations.AddField(
            model_name='kpiusergoalvalues',
            name='in_percentage',
            field=models.BooleanField(default=True, help_text='Indicates if actual and target values should be displayed with % symbol'),
        ),
        migrations.AddField(
            model_name='kpiusertarget',
            name='in_percentage',
            field=models.BooleanField(default=True, help_text='Indicates if target value should be displayed with % symbol'),
        ),
    ]

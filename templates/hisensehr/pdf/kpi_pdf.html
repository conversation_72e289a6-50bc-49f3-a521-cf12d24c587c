{% extends "hisensehr/pdf/print_layout.html" %}
{% load my_filters static %}
{% block body_block %}
<div class="pdf-body-content" style="min-height: 900px;">
  <h4 style="text-align:center">QUATERLY KPI - {{ current_user.profile.department.name }}  - {{ current_user.first_name }} {{ current_user.last_name }}</h4>
  <table width="100%">
    <tr>
      <td style="min-width:300px;">Full Name</td>
      <td style="min-width:300px;">Department</td>
      <td style="min-width:300px;">Title</td>
    </tr>
    <tr>
      <td>{{ current_user.first_name }} {{ current_user.last_name }}</td>
      <td>{{ current_user.profile.department.name }}</td>
      <td>{{ current_user.profile.designation.name }}</td>
    </tr>
    <tr><td colspan="3">&nbsp;</td></tr>
  </table>
    <table class="kpi_table">
      <thead>
        <tr class="table_head">
          <td >KPI</td>
          <td >Measurement</td>
          <td >Goal</td>
          <td >Weight</td>
          <td >Target {{ current_period }}</td>
          <td >Actual {{ current_period }}</td>
          <td >% Achieved(Actual / Target)</td>
          <td style="min-width:100px;">Weightage Score(%Achieved x Weightage%)</td>
          <!-- <td >Remarks</td> -->
        </tr>
      </thead>
      <tbody class="tbody-white">
        {% if user_goals %}
          {% regroup user_goals by kpiuser_goal.kpi_goal.kpi_category.name  as category_list %} 
            {% for category in category_list %}
              <tr>
                <td scope="col" rowspan="{{ category.list|length|to_int|add:1 }}">{{ category.grouper }}</td>                            
                {% for goal in category.list %}
                <tr>
                  <td class="border_left_td">{{ goal.kpiuser_goal.kpi_goal.measurement_type.name }}</td>
                  <td>{{ goal.kpiuser_goal.kpi_goal.name }} : <b>{{ goal.goal_value }}</b>; Max Finish Rate = <b>{{ goal.max_finish_rate }}</b>%</td>
                  <td>{{ goal.weight }}%</td>
                  {% get_percentage_flags goal as percentage_flags %}
                  <td>{{ goal.target|format_with_percentage:percentage_flags.target_percentage }}</td>
                  <td>{{ goal.actual|format_with_percentage:percentage_flags.actual_percentage }}</td>
                  <td class="text-center">
                    {% if goal.id %} 
                    {% get_achieved_percentage goal.actual goal.target goal.max_finish_rate goal.kpiuser_goal.kpi_goal.measurement_type.calculation as achieved %}
                        {{ achieved|floatformat:2 }}%
                    {% else %}
                      <div class="text-center">--</div>
                    {% endif %}
                  </td>
                  <td>
                  {% if goal.id %}
                  {% get_weightage_score goal.actual goal.target goal.weight goal.max_finish_rate goal.kpiuser_goal.kpi_goal.measurement_type.calculation as score %}
                    <span class="score">{{ score|floatformat:2 }}</span>%
                  {% else %}
                    <div class="text-center">--</div>
                  {% endif %}
                  </td>
                  <!-- <td>
                    {% if goal.remark %}
                        {{ goal.remark }} 
                    {% else %}
                        <div class="text-center">--</div>
                    {% endif %}
                  </td> -->
              </tr>
            {% endfor %}
            </tr> 
          {% endfor %}
        {% endif %}
        <tr>
          <td colspan="6">&nbsp;<td>
          <td><span id="total_score">{{user_goals.0.kpiuser_timeperiod.score}}%</span></td>
          <td>&nbsp;</td>
        </tr>
      </tbody>
    </table>
    <table class="table">
      <tr><td colspan="3">KPI - Guidelines</td></tr>
      <tr><td colspan="3">
        {% if guideline %}
          <div class="guide-lines">
            {{guideline.content|safe}}
          </div>
        {% endif %}
        </td>
      </tr>
    </table>
    <table class="table">
      <tr>
        <td>KPI Achievement</td>
        <td>Bonus Payout % </td>
        </tr>
        <tr>
          <td style="text-align: center;">80% or above</td>
          <td><p>Company KPI*30%
            +
            Personal KPI*70%</p></td>
        </tr>
        <tr>
          <td style="text-align: center;">Less than 80%</td>
          <td><p>Mgmt Decision</p></td>
        </tr>
        
    </table>
    {% comment %} <table class="table">
      <tr><td colspan="3">KPI - Guidelines</td></tr>
      <tr><td colspan="3">The target should be higher than the Y.O.Y. If the actual achievement is lower than Y.O.Y, then the corresponding KPI will be 0.</td></tr>
      <tr><td colspan="3">If the profit is 0 or negative, the bonus for corresponding staff will be 0 (If the sales GM is handle 2 categories, then the bonus will be deducted according to the ratio.)</td></tr>
      <tr>
        <td>No Dead Debts :</td>
        <td colspan="2">The amount of bad debt is all the money lost for this debt, not that amount after insurance.
          1. bad debt or total amount of bad debt exceeds 0.1% of their personal sales revenue, 15% deduction of total bonus (including quarterly bonus); 
          2. bad debts or total amount of bad debt exceeds 0.5% of their personal sales revenue, 30% deduction of total bonus (including quarterly bonus); 
          3. bad debts or  total amount of bad debt exceeds 1% of their personal sales revenue, 100% deduction of total bonus (including quarterly bonus). 
          4. If the Product line loses money - the bonus pay out will be ZERO</td>
      </tr>
      <tr>
        <td rowspan="4">Your bonus percentage as termed in your offer letter will be based on your KPI achievement & peformance evaluation. Bonus payout % will be based on the column illustrated aside. 
          However all bonus payments are subject to overall KPI evaluation and as decided by the management. This document for the bonus process will superside all previous documents, letters issued before . </td>
        </tr>
        <tr>
        <td>KPI Achievement</td>
        <td>Bonus Payout % </td>
        </tr>
        <tr>
          <td style="text-align: center;">80% or above</td>
          <td><p>Company KPI*30%
            +
            Personal KPI*70%</p></td>
        </tr>
        <tr>
          <td style="text-align: center;">Less than 80%</td>
          <td><p>Mgmt Decision</p></td>
        </tr>
        
    </table> {% endcomment %}
    {% if user_time_period.status == 5 %}
      
      <table style="width:100%">
        <tr><td colspan="2">&nbsp;</td></tr>
        <tr>
          <td style="width:50%">
            <table>
              <tr>
                <td>Employee Name : </td>
                <td>
                  {{user_time_period.kpi_user_year.user.first_name}} {{user_time_period.kpi_user_year.user.last_name}}
                </td>
              </tr>
              <tr>
                <td>Employee signature ： </td>
                <td>
                  {% if user_time_period.employee_signature %}
                  <div class="sign-sec">
                    <div class="sign-wrapper">
                      <img src="{{ user_time_period.employee_signature.url }}">
                    </div>
                  </div>
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td>Date : </td>
                <td>
                  {{ user_time_period.employee_signature_date }}
                </td>
              </tr>
            </table>
          </td>  
          <td style="width:50%">
            <table>
              <tr>
                <td>HOD Name : </td>
                <td>
                  {{user_time_period.approved_by.first_name}} {{user_time_period.approved_by.last_name}}
                </td>
              </tr>
              <tr>
                <td>HOD signature ： </td>
                <td>
                  {% if user_time_period.hod_signature %}
                  <div class="sign-sec">
                    <div class="sign-wrapper">
                      <img src="{{ user_time_period.hod_signature.url }}">
                    </div>
                  </div>
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td>Date : </td>
                <td>
                  {{ user_time_period.hod_date }}
                </td>
              </tr>
            </table>
          </td>  
        </tr>
      </table>
    {% endif %}
</div>
{% endblock %}

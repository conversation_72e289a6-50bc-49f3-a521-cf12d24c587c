{% extends "hisensehr/layout/user_base.html" %}
{% load static %}
{% load my_filters %}
{% block body_block %}
<style>
  .arrow {
      display: inline-block;
      width: 0;
      height: 0;
      /* margin-left: 10px; */
      vertical-align: middle;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 10px solid white;
      cursor: pointer;
      transition: transform 0.3s ease;
    }

    .arrow.up {
      transform: rotate(180deg);
    }
</style>


<div class="content">
    <div class="main-contents full-screen">
        <div class="page-top">
            <div> 
              <h4>KPI Approval</h4>
              <p><a href="{% url 'appdashboard:dashboard'%}">Dashboard</a><span>/<span><a href="" class="current-page">KPI Approval</a><span></p>
        </div>
    </div> 
    <div class="main-content-inner">
      <div class="card-boxes bg-white mt-20">
        <div class="d-flex profile-basic-info kpi-top">
          <div class="profile-info-item align-items-center">
            <span>Full Name</span>
            <p>{{ current_user.approved_rejected_user.first_name }} {{ current_user.approved_rejected_user.last_name }}</p>
          </div>
          <div class="profile-info-item">
            <span>Department</span>
            <p>{{ current_user.approved_rejected_user.profile.department.name }}</p>
          </div>
          <div class="profile-info-item" >
            <span>Title</span>
            <p>{{ current_user.approved_rejected_user.profile.designation.name }}</p>
          </div>
          <div class="profile-info-item" >
            <span>KPI Year - Time period</span>
            <p>{{ current_year_name }} - {{ current_quater_name }}</p>
          </div>
        </div>
   
        <div class="navigation_menu" id="navigation_menu">
            <ul class="navigation_tabs" id="navigation_tabs">
            {% if kpi_apprv_list %}
              {% for assignee in kpi_apprv_list %}  
                  {% if assignee.kpilevel_id_id == 2 %} 
                      <li class="{% if assignee.is_approve %}tab_inactive{% else %}tab_disabled{% endif %}">
                          <span class="top_status">{% if assignee.is_approve %}Created & Approved{% else %}Created{% endif %}</span>
                          <a href="#">{{assignee.added_by.first_name}} {{assignee.added_by.last_name}}</a>
                          <p class="status_date">{{assignee.updated_at|time_uae_format}}</p>
                      </li>
                  {% else %}
                      <li class="{% if assignee.is_approve and assignee.approval_status == 'approved' %}tab_inactive{% elif not assignee.is_approve and assignee.approval_status == 'pending' %}tab_disabled{% elif not assignee.is_approve and assignee.approval_status == 'rejected' %}tab_rejected{% endif %}">
                          <span class="top_status">{{assignee.approval_status|getCapitialized}}</span>
                          <a href="#">{{assignee.added_by.first_name}} {{assignee.added_by.last_name}}</a>
                          <p class="status_date">{{assignee.updated_at|time_uae_format}}</p>
                      </li>
                  {% endif %}
              {% endfor %}
            {% endif %}
            
            </ul>
        </div>
    </div>

        
        <div class="table-card-block mt-20 ">
          <div class="table-top d-flex justify-content-between align-items-center p-20">
              <h4>KPI - Overview</h4>
              <div class="d-flex">
                <div class="top-action" id="pdf_link" style="display:none">     
                  <a href="javascript:void(0)" onclick="generateMyKpiPdf()"  data-toggle="tooltip" title="Download PDF">
                    <img src="{% static 'admin/assets/images/downl_icon.svg' %}">
                  </a>
                </div>
                <div class="top-action">
                  <a href="{% url 'appdashboard:kpi_approve_view'%}"  data-toggle="tooltip" title="KPI Approve">
                    <img src="{% static 'admin/assets/images/back-button.svg' %}">
                  </a>   
                </div>            
              </div>
          </div>
          <div class="table_wrapper hod_table_wrapper kpi_goal_overview_height">
              <table class="table table-borderless table-space-between table-perfo-mngmt view_table">
                  <thead>
                    <tr class="fw-m table_head">
                      <td style="min-width:300px;">KPI</td>
                      <td style="min-width:150px;">Measurement</td>
                      <td style="min-width:400px;">Goal</td>
                      <td >Weight</td>
                      <td >Target {{ current_period }}</td>
                      <td >Actual {{ current_period }}</td>
                      <td >% Achieved(Actual / Target)</td>
                      <td >Weightage Score(%Achieved x Weightage%)</td>
                      <td style="min-width:400px;">Remarks</td>
                    </tr>
                  </thead>
                  <tbody class="tbody-white">
                    {% if user_goals %}
                    {% regroup user_goals by kpiuser_goal.kpi_goal.kpi_category.name  as category_list %} 
                    {% for category in category_list %}
                    <tr>
                      <td scope="col" rowspan="{{ category.list|length|to_int|add:1 }}" >{{ category.grouper }}</td>
                      {% for goal in category.list %}
                      <tr>
                            <td class="border_left_td">{{ goal.kpiuser_goal.kpi_goal.measurement_type.name }}</td>
                            <td>{{ goal.kpiuser_goal.kpi_goal.name }} : <b>{{ goal.goal_value }}</b>; Max Finish Rate = <b>{{ goal.max_finish_rate }}</b>%</td>
                            <td>{{ goal.weight }}%</td>
                            {% get_percentage_flags goal as percentage_flags %}
                            <td>{{ goal.target|format_with_percentage:percentage_flags.target_percentage }}</td>
                            <td>{{ goal.actual|format_with_percentage:percentage_flags.actual_percentage }}</td>
                            <td class="text-center">
                              {% if goal.id %} 
                                {% get_achieved_percentage goal.actual goal.target goal.max_finish_rate goal.kpiuser_goal.kpi_goal.measurement_type.calculation as achieved %}
                                    {{ achieved|floatformat:2 }}%
                              {% else %}
                                <div class="text-center">--</div>
                              {% endif %}
                            </td>
                            <td class="text-center">
                              {% if goal.id %}
                                {% get_weightage_score goal.actual goal.target goal.weight goal.max_finish_rate goal.kpiuser_goal.kpi_goal.measurement_type.calculation as score %}
                                        <span class="score">{{ score|floatformat:2 }}</span>%
                              {% else %}
                                <div class="text-center">--</div>
                              {% endif %}
                            </td>
                            <td>
                                {% if goal.remark %}
                                    {{ goal.remark }} 
                                {% else %}
                                    <div class="text-center">--</div>
                                {% endif %}
                            </td>
                          </tr>
                        {% endfor %}
          </tr> 
        {% endfor %}
        <tr class="green-bottom">
            <td colspan="7" style="text-align:right">Total</td>
            <!-- <td>{% get_weightage_score goal.actual goal.target goal.weight goal.max_finish_rate goal.kpiuser_goal.kpi_goal.measurement_type.calculation as score %}
              <span class="score">{{ score|floatformat:2 }}</span>%</td> -->
            <td style="padding-left: 4%;" colspan="7">{{user_goals.0.kpiuser_timeperiod.score}}%</td>
            <td></td>
            <td id="total_score"></td>
            <td></td>
        </tr>


        {% if appreciation_list %}
          <tr class="accordion-toggle green-bottom">
            <td colspan="7" style="text-align:right; cursor: pointer;" onclick="toggleAccordion(this)">
              Letter of praise
            </td>
            <td colspan="9" style="padding-left: 4%;" onclick="toggleAccordion(this)">
              {{ appreciation_value_sum }} %
            </td>
            <td onclick="toggleAccordion(this)"><span class="arrow up"></span></td>
          </tr>

          {% for appreciation_val in appreciation_list %}
            <tr class="accordion-appriciation-content" style="display: table-row;">
              <td colspan="7" style="text-align:right;">{{ appreciation_val.comments }} (Dated on: {{ appreciation_val.modification_date }})</td>
              <td colspan="9" style="padding-left: 4%;">{{ appreciation_val.value }}%</td>
              <td style="display: flex; flex-direction: row; width: 100px; align-items: center;">
                <div style="margin-right: 10px;">
                    {% if appreciation_val.documents %}
                    <a href="{{ appreciation_val.documents.url }}" download="{{ appreciation_val.documents.name }}" data-file-name="{{ appreciation_val.documents }}" id="downloadButton" data-toggle="tooltip" class="imgButton" title="Download">
                        <img src="/static/admin/assets/images/kpi_goal_download.png" style="width: 20px; height: 20px;">
                    </a>
                    {% endif %}
                </div>

            </td>
            </tr>
          {% endfor %}
        {% endif %}

        {% if criticism_list %}
          <tr class="accordion-toggle green-bottom">
            <td colspan="7" style="text-align:right; cursor: pointer;" onclick="toggleAccordionCriticism(this)">
              Letter of criticism
            </td>
            <td colspan="9" style="padding-left: 4%;" onclick="toggleAccordionCriticism(this)">
              {{ criticism_value_sum }} %
            </td>
            <td onclick="toggleAccordionCriticism(this)"><span class="arrow up"></span></td>

          </tr>

          {% for criticism_val in criticism_list %}
            <tr class="accordion-appriciation-content" style="display: table-row;">
              <td colspan="7" style="text-align:right; ">{{ criticism_val.comments }} (Dated on: {{ criticism_val.modification_date }})</td>
              <td colspan="9" style="padding-left: 4%; ">{{ criticism_val.value }}%</td>
              <td style="display: flex; flex-direction: row; width: 100px; align-items: center;">
                <div style="margin-right: 10px;">
                    {% if criticism_val.documents %}
                    <a href="{{ criticism_val.documents.url }}" download="{{ criticism_val.documents.name }}" data-file-name="{{ criticism_val.documents }}" id="downloadButton" data-toggle="tooltip" class="imgButton" title="Download">
                        <img src="/static/admin/assets/images/kpi_goal_download.png" style="width: 20px; height: 20px;">
                    </a>
                    {% endif %}
                </div>

            </td>
            </tr>
          {% endfor %}
        {% endif %}
        
        {% if user_goals.0.kpiuser_timeperiod.updated_score %}
          <tr class="green-bottom">
              <td colspan="7" style="text-align:right">Modified total</td>
              <td colspan="7" style="padding-left: 4%;" >{{ user_goals.0.kpiuser_timeperiod.updated_score }}%</td>
              <td></td>
              <td id="total_score"></td>
              <td></td>
          </tr>
        {% endif %}
      {% else %}
        <tr>
            <td colspan="9" style="text-align:center;">Approved goals not found for this year<td>
        </tr>
      {% endif %}
                  </tbody>
                </table>
          </div>
        </div>


        <!-- {% if modified_log %}
        <div class="main-content-inner apply-leave-page">
          <div class="card-boxes apply-leave bg-white mt-20">
            <div class="table-top">
              <h4>Performance Modification</h4> 
            </div>
                
            <ul class="form-box" style="padding: 20px 20px 20px 20px">
              <li>
                  <div class="input-box" style="width: 40%!important;">
                      <label for="revision_type">Score revision type</label>
                      <input type="text" name="revision_value" id="revision_value" value="{% if modified_log.value_type == 1 %} Letter of criticism {% else %} Letter of praise {% endif %}" readonly style="height: 40px;">
                  </div>
                  <div class="input-box" style="width: 40%!important;">
                      <label for="revision_value">Revision value (in %)</label>
                      <input type="text" name="revision_value" id="revision_value" value="{{ modified_log.value }}" readonly style="height: 40px;">
                  </div>
                </li>
                <li>
                  <div class="input-box" style="width: 40%!important;">
                      <label for="revision_value">Acheived total weightage</label>
                      <input type="text" name="revision_value" id="revision_value" value="{{ user_goals.0.kpiuser_timeperiod.score }} %" readonly style="height: 40px;">
                  </div>
                    <div class="input-box" style="width: 40%!important;">
                        <label for="revision_value">Modified total weightage</label>
                        <input type="text" name="revision_value" id="revision_value" value="{{ modified_log.updated_score }} %" readonly style="height: 40px;">
                    </div>
                </li>
                <li>
                    <div class="input-box" style="width: 100%!important;">
                        <label for="reason">Comments</label>
                        <textarea name="reason" id="reason" cols="30" rows="20" style="width:84%" readonly>{{ modified_log.comments }}</textarea>
                    </div>
                </li>
            </ul>

    
            </div>
          </div>
        {% endif %} -->



        {% if user_goals %}
          <div class="card-boxes bg-white mt-20">
              <div class="table-top">
                <h4>KPI - Guidelines</h4> 
              </div>
              {% if guideline %}
                <div class="guide-lines mb-20">
                  {{guideline.content|safe}}
                </div>
              {% endif %}
              <div class="kpi-achivements">
                  <div class="achive-table-wrapper">
                    <table>
                      <thead>
                        <tr>
                          <th style="text-align: center;">KPI Achievement</th>
                          <th>Bonus Payout % </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td style="text-align: center;"><p class="increase">80% or above</p></td>
                          <td><p>Company KPI*30% + Personal KPI*70%</p></td>
                        </tr>
                        <tr>
                          <td style="text-align: center;"><p class="decrease">Less Than 80%</p></td>
                          <td><p>Management Decision</p></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="note">
                    <p class="text-red">Note:</p>
                    <p>Weightage Score = Achieved % x Weightage %</p>
                  </div>
              </div>
            </div>

            {% if previous_level == 1 %}
              <div class="card-boxes accordian-boxes bg-white mt-20 p-0">
                <div class="table-top accordion-header d-flex justify-content-between p-20">
                  <h4>Comments</h4> 
                  <div class="accordion-btn"><span class="icon-Vector-2"></span></div>
                </div>
                <div class="accordian-content p-20">
                <div class="comments">
                  <ul class="comments-info">
                  {% if kpi_remarks_list %}
                    {% for comments in kpi_remarks_list %}  
                      {% if comments.remarks %}
                        <li class="comments-info-item">
                            <div class="flex-grow-1">
                              <span>{{comments.remarks}}</span>
                                <div class="comments_info">
                                  <p style="margin-right: 5px;">{{comments.kpi_added_by.first_name}} {{comments.kpi_added_by.last_name}}</p>
                                  <p>{{comments.updated_at|time_uae_format}}</p>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                              {% if comments.approval_status == 'approved' %}
                                <span class="table-bg-green">{{comments.approval_status}}</span>
                              {% else %}
                                <span class="table-bg-red">{{comments.approval_status}}</span>
                              {% endif %}

                            </div>
                            
                        </li>
                    {% endif %}
                    {% endfor %}
                  {% else %}
                  <p style="text-align: center;font-size: 14px;color:#979797">No comments</p> 
                  {% endif %}
                  </ul>
                  </div>
                  </div>
            </div>
          {% endif %}

          </div>

          {% if kpi_current_user_level_status == False%}
            {% if goal_approval_status.status == 1 %}
              <div class="card-boxes bg-white mt-20">
                <div class="d-flex flex-column align-items-center justify-content-center p-20">
                  <label class="error mb-3">Goals need to be approved or activated before proceeding. Please complete this step first.</label>
                  <a href="{% url 'appdashboard:goal_approve_detail_view' current_user.approved_rejected_user.id|encrypt_value goal_approval_status.kpi_year.id|encrypt_value %}" class="gradient-button">Approve Goal</a>
                </div>
              </div>
            {% else %}
              <div class="card-boxes bg-white mt-20">
                <div class="d-flex button-block  justify-content-end">
                  {% if button_flag == 1 %}
                    {% if reject_button_flag == 1 and approve_reject_status != 'rejected' %}
                      <a href="javascript:void(0);" data-fancybox data-src="#reject_kpi"  class="white-button" style="text-decoration:none;"><span class="btn-helper"></span>Reject</a>
                    {% else %}
                      <a href="javascript:void(0);"  class="white-button disabled" style="text-decoration:none;"><span class="btn-helper"></span>Reject</a>
                    {% endif %}
                    {% if approve_reject_status != 'approved' %}
                      {% if disable_button == True %}
                          <a href="javascript:void(0);" class="gradient-button" id="approveButton" style="text-decoration:none;"><span class="btn-helper"></span>Approve</a>
                      {% else %}
                          <a href="javascript:void(0);" data-fancybox data-src="#approve_kpi" class="gradient-button" style="text-decoration:none;"><span class="btn-helper"></span>Approve</a>
                      {% endif %}
                    {% else %}
                      <a href="javascript:void(0);"  class="gradient-button disabled" style="text-decoration:none;"><span class="btn-helper"></span>Approve</a>
                    {% endif %}
                  {% else %}
                    <a href="javascript:void(0);"  class="white-button disabled" style="text-decoration:none;"><span class="btn-helper"></span>Reject</a>
                    <a href="javascript:void(0);"  class="gradient-button disabled" style="text-decoration:none;"><span class="btn-helper"></span>Approve</a>
                  {% endif %}
                </div>
              </div>
            {% endif %}
          {% endif %}
          </div>

          {% if previous_level == 1 %}
            <div class="d-flex flex-row justify-content-between align-items-stretch mt-20 m-10-minus " >
              <div class="w-30 mx-10">
                  <div class="card-boxes bg-white p-0 h-100">
                      <div class="chat-top">
                          <h4>Chats</h4>
                      </div>
                      <ul class="chat-choose-list">
                        {% for assignee in kpi_apprv_list %}  
                          {% if assignee.added_by.id != request.user.id %}
                          <li class="chat-choose-item" onclick="toggleCheckbox(this)">
                            <div><label class="checkmark-container"><input type="checkbox" value="{{assignee.added_by.id}}" class="category_ckeck" data-id="25"><span class="checkmark"></span></label></div>
                              {% if assignee.added_by.profile and assignee.added_by.profile.profile_pic %}
                              <div class="profile-round" style="width:32px; height:32px;">
                                      <img src="{{assignee.added_by.profile.profile_pic}}" style="background-image:url({{assignee.added_by.profile.profile_pic}});"   alt="icon">
                              </div>
                              {% else %}
                              <div class="profile-round" style="width:32px; height:32px;">
                                      <img src="{% static '/admin/assets/images/user.png' %}" style="background-image:url({% static '/admin/assets/images/user.png' %});" alt="icon">
                              </div>
                              {% endif %}
                              <div class="ml-20"><h5>{{assignee.added_by.first_name}} {{assignee.added_by.last_name}}</h5></div>
                          </li>
                          {% endif %}
                        {% endfor %}
                      </ul>
                  </div>
              </div>
              <div class="w-70 mx-10">
                  <div class="card-boxes bg-white p-0 h-100">
                      <div class="chat-top">
                          <!-- <div class="profile-round" style="width:42px; height:42px;"><img src="../assets/images/profile-pic.jpg" alt="profile_pic"></div>
                          <div class="ml-20">
                              <h5>Sales Manager</h5>
                              <span>Active Now</span>
                          </div> -->
                      </div>
                      <ul class="chat-body">
                          {% for msg in approval_messages %}
                            {% check_equal_string  msg.receiver request.user.first_name request.user.last_name as result %}
                            {% if result %}                            
                              <li class="chat-item item-receive ">
                                  <div class="d-flex flex-column chat-item-wrapper">
                                    <span class="chat_author">{{msg.sender.first_name}} {{msg.sender.last_name}}</span>
                                      <div class="chat-content">
                                          <p>{{msg.message}}</p>
                                      </div>
                                      <div class="chat-time">{{msg.updated_at|time_format}}</div>
                                  </div>
                              </li>  
                            {% elif msg.sender.id == request.user.id %}
                              <li class="chat-item item-send  ">
                                  <div class="d-flex flex-column chat-item-wrapper">
                                    <span class="chat_author">@{{msg.receiver}}</span>
                                      <div class="chat-content">
                                          <p>{{msg.message}}</p>
                                      </div>
                                      <div class="chat-time">{{msg.updated_at|time_format}}</div>
                                  </div>
                              </li>  
                            {% endif %}
                          {% endfor %}
                            
                      </ul>
                      <div class="chat-bottom">
                          <div class="form-field-box">
                              <input type="text" id="message_content" placeholder="Type a message...">
                          </div>
                          <button type="submit" class="gradient-button" id="message_button"><span class="btn-helper"></span>Send</button>
                      </div>
                  </div>
              </div>
            
            {% endif %}
        </div>
      {% endif %}
</div>


<div class="kpi_approve_popup" id="approve_kpi" style="display:none;width: 50%;">
  <div class="pop-body">
      <h5>Approval</h5>
      <form action="{% url 'appdashboard:kpi_approval' %}" class="insurance-policy-form" name="approve_form" id="approve_form" method="post">
        {% csrf_token %}
        <div>
            <div class="chat-bottom" style="border-top: unset!important;">
              <div class="form-field-box ">
                <label for="">Comments: </label>
                <textarea class="form-control policy_desc w-100 remarks" onfocus="placeCursorToEnd('approve')"  id="approve" name="comments" style="cursor: default;">
                </textarea> 
              </div>
              <!-- <button type="submit" class="gradient-button"><span class="btn-helper"></span>Send</button> -->
            </div>
            <div class="btnAprve">
              <button type="submit" class="gradient-button " id="approve_popup_btn" onclick="approve_reject('approve_popup_btn')"><span class="btn-helper"></span>Approve</button>
            </div>
        </div>
          <input type="hidden" value="{{ qtr_id }}" id="qtr_id" name="qtr_id">
          <input type="hidden" value="0" id="yearid" name="yearid">
          <input type="hidden" value="{{ current_user.approved_rejected_user.id }}" id="kpi_id" name="kpi_id">
          <input type="hidden" name="msg_url" id="msg_url" data-url="{% url 'appdashboard:approval_message' %}">
      </form>
  </div>
</div>

<!-- Popup Ends -->

<!-- Popup -reject -->
<div class="kpi_reject_popup" id="reject_kpi" style="display:none;width: 50%;">
  <div class="pop-body">
      <h5>Reject</h5>
      <form action="{% url 'appdashboard:kpi_reject' %}" class="insurance-policy-form" name="reject_form" id="reject_form" method="post">
          {% csrf_token %}
          <div>
            <div class="chat-bottom" style="border-top: unset!important;">
              <div class="form-field-box ">
                <label for="">Comments:<span class="text-danger">*</span></label>
                <textarea class="form-control policy_desc w-100 remarks" id="reject" onfocus="placeCursorToEnd('reject')"  name="comments" style="cursor: default;">
                </textarea> 
                <span class="error" id="reject-comment-error" style="color: red;"></span>
              </div>
              <!-- <button type="submit" class="gradient-button"><span class="btn-helper"></span>Send</button> -->
            </div>
            <div class="btnAprve">
              <button type="button" class="gradient-button " id="reject_popup_btn" onclick="approve_reject('reject_popup_btn')" ><span class="btn-helper"></span>Reject</button>
            </div>
        </div>
          <input type="hidden" value="{{ qtr_id }}" id="qtr_id" name="qtr_id">
          <input type="hidden" value="{{ current_user.approved_rejected_user.id }}" id="kpi_id" name="kpi_id">
      </form>
  </div>
</div>

<style>
  .table-bg-green,.table-bg-red {
    text-transform: capitalize;
  }
</style>
{% endblock %}
{% block page_script %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js" integrity="sha512-rstIgDs0xPgmG6RX1Aba4KV5cWJbAMcvRCVmglpam9SoHZiUCyQVDdH2LPlxoHtrv17XWblE/V/PP+Tr04hbtA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="{% static 'admin/assets/js/kpi-approval-level.js' %}?v={% now 'd_m_yH:i:s'%}"></script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
  function placeCursorToEnd(id) {
    var textarea = document.getElementById(id);
    var value = textarea.value;
    textarea.value = '';  
    textarea.value = value.trim();  
    textarea.setSelectionRange(textarea.value.length, textarea.value.length); 
  }
</script>
<script>
  function approve_reject(val){
    if (val == 'approve_popup_btn')
    {
      $('#'+val).text('Approving')
      $('#'+val).prop('disabled', true)
      $('#approve_form').submit()
    }
    else
    {
      var comment = $('#reject').val()
      if(comment == null || comment == ""){
          $("#reject-comment-error").text("Comment should not be empty")
          setTimeout(function() {
              $("#reject-comment-error").text('')
          },1000)
      }
      else{
        $('#'+val).text('Rejecting')
        $('#'+val).prop('disabled', true)
        $('#reject_form').submit()
      }
    }
  }

  $(document).ready(function() {
    $(".accordian-boxes:first-of-type").addClass("show");
    $(".accordian-boxes").on("click", ".accordion-header", function() {
        $(".accordian-content").slideUp().removeClass("flipInX");
        $(".accordion-btn").removeClass("close");
        if ($(this).next().is(":hidden")) {
            $(this).next().slideDown().addClass("flipInX");
            $(this).find(".accordion-btn").addClass("close");
        } else {
            $(this).next().slideUp();
            $(this).find(".accordion-btn").addClass("open");
        }
    });
    $(document).on('click', '.profile_cancel', function() {
        $('.accordian-content').slideUp();
        $('.accordion-btn').removeClass('close');
        $('.accordion-btn').addClass('open');
    });
});

</script>

{% if messages %}
  {% for message in messages %}
    <script>
      $(".msg_desc").text('{{ message }}')
      $("#flash_message_success").attr("style","display:block;")
      setTimeout(function(){
        $("#flash_message_success").attr("style","display:none;")
      },3000)

      </script>
  {% endfor %}
{% endif %}

{% endblock page_script %}
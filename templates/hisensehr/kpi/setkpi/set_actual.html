{% load static my_filters %}
{% if not probation_completed  %}
  <div class="table_wrapper hod_table_wrapper" id="kpi_table">
    <div class="text-center p-5">
        <h6 class="error_input" align="center">{{selected_user.first_name}} {{selected_user.last_name}} is in probation period. Joining date is {{selected_user.profile.date_of_joining|date:'d M y'}}.</h6>
    </div>
  </div>
{% else %}
    <table class="table table-borderless table-space-between table-target">
      <thead>
        <tr class="fw-m table_head">
          <td style="min-width:300px;">KPI</td>
          <td style="min-width:150px;">Measurement</td>
          <td style="min-width:400px;">Goal</td>
          <td>Weight</td>
          <td>Target {{ current_period }}</td>
          <td style="min-width:200px;">Actual {{ current_period }}</td>
          <td>Remarks</td>
        </tr>
      </thead>
      <tbody class="tbody-white">
          {% if user_goals %}
          {% regroup user_goals by kpi_goal__kpi_category__name  as category_list %} 
          {% for category in category_list %}
          <tr>
              <td scope="col" rowspan="{{ category.list|length|to_int|add:1 }}">{{ category.grouper }}</td>
                  {% for goal in category.list %}
                  <tr>
                    <td class="border_left_td">{{ goal.kpi_goal__measurement_type__name }}</td>
                    <td>{{ goal.kpi_goal__name }} : <b>{{ goal.goal_value }}</b>; Max Finish Rate = <b>{{ goal.max_finish_rate }}%</b></td>
                    <td>{{ goal.weight }}%</td>
                    <td>
                      {% if user_goals_query_set %}
                      {% for goal_obj in user_goals_query_set %}
                      {% if goal_obj.id == goal.id %}
                      {% if goal_obj.user_target.first %}
                      {{ goal_obj.user_target.first.target|format_with_percentage:goal_obj.user_target.first.in_percentage }}
                      {% endif %}
                      {% endif %}
                      {% endfor %}
                      {% endif %}
                    </td>
                    <td>
                      <input id="set_actual_{{goal.id}}" type="number" data-id="{{ goal.id }}" data-edited="false" min="0" class="set_goal_actual form-control" placeholder="Enter Actual"
                      {% if user_goals_query_set %}
                      {% for goal_obj in user_goals_query_set %}
                      {% if goal_obj.id == goal.id %}
                      value="{{ goal_obj.user_actual.first.actual }}"
                      {% endif %}
                      {% endfor %}
                      {% endif %}
                      >
                    </td>
                    <td>
                      <a href="javascript:void(0);" data-fancybox="" data-src="#{{ goal.id }}">
                          <img src="{% static 'admin/assets/images/doc_icon.svg' %}" alt="">
                      </a>
                      <div style="display:none;width:50%" class="p-5" id="{{ goal.id }}">
                          <h3>Remarks</h3>
                          <textarea id="remark_{{goal.id}}" style="width:100%" rows="10" class="mt-2 remarks">{% for goal_obj in user_goals_query_set %}{% if goal_obj.id == goal.id %}{% if goal_obj.user_actual.first.remark %}{{ goal_obj.user_actual.first.remark }}{% endif %}{% endif %}{% endfor %}</textarea>
                          <div class="mt-5">
                              <button type="submit" data-action="ok" data-id="{{ goal.id }}" class="gradient-button remark"><span class="btn-helper"></span>Add</button>
                          </div>
                        </div>
                  </td>
                  </tr>
                  {% endfor %}
          </tr> 
          {% endfor %}
          <tr class="green-bottom">
              <td colspan="3" style="text-align:right">Total</td>
              <td colspan="4">{{ total_weight }}%</td>
          </tr>
          <tr>
            <td colspan="7">
            <div class="d-flex button-block  justify-content-end">
              <button type="submit" id="set_actual" data-url="{% url 'appdashboard:set_actual' %}" class="gradient-button"><span class="btn-helper"></span>Save</button>
            </div>
            </td>
          </tr>
          {% else %}
          <tr>
            <td colspan="6" style="text-align:center;">Target values are not set or goals are not approved<td>
          </tr>
          {% endif %}
          
      </tbody>
    </table>
{% endif %}

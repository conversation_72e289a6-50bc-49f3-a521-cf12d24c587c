{% extends "hisensehr/layout/user_base.html" %}
{% block body_block %}
{% load static %} 
{% load my_filters %}
<style>
  .arrow {
      display: inline-block;
      width: 0;
      height: 0;
      /* margin-left: 10px; */
      vertical-align: middle;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 10px solid white;
      cursor: pointer;
      transition: transform 0.3s ease;
    }

    .arrow.up {
      transform: rotate(180deg);
    }
</style>
<div class="content">
  <input type="hidden" id="selected_period" value="{{ selected_period }}">
  <input type="hidden" id="current_period_id" value="{{ current_period_id }}">
  <input type="hidden" name="kpi_approved" id="kpi_approved" value="{{user_time_period.status}}">
            
    <div class="main-contents full-screen">
      <div class="page-top">
          <div>
            <h4>KPI</h4>
            <p><a href="{% url 'appdashboard:dashboard'%}">Dashboard</a><span>/<span><a href="" class="current-page">KPI</a><span></p>
          </div>
      </div> 
        <div class="main-content-inner">
          <div class="card-boxes bg-white mt-20">
              <div class="d-flex profile-basic-info">
                <div class="profile-info-item align-items-center">
                  <span>Full Name</span>
                  <p>{{ current_user.first_name }} {{ current_user.last_name }}</p>
                </div>
                <div class="profile-info-item">
                  <span>Department</span>
                  <p>{{ current_user.profile.department.name }}</p>
                </div>
                <div class="profile-info-item flex-grow-1" >
                  <span>Title</span>
                  <p>{{ current_user.profile.designation.name }}</p>
                </div>
              </div>
          </div>
          <div class="table-card-block mt-20">
            <div class="table-top d-flex justify-content-between align-items-center p-20">
                <h4>KPI - Overview</h4>
                <div class="d-flex">
                  <div class="top-action" id="pdf_link" style="display:flex">     
                    <a href="{% url 'appdashboard:my_kpi_pdf' current_period_id current_user.id %}"  data-toggle="tooltip" title="Download Sheet">
                      <img src="{% static 'admin/assets/images/downl_icon.svg' %}">
                    </a>
                  </div>
                  <div class="top-action">
                    <a href="{% url 'appdashboard:my_kpi'%}"  data-toggle="tooltip" title="My KPI">
                      <img src="{% static 'admin/assets/images/back-button.svg' %}">
                    </a>   
                  </div>            
                </div>
            </div>
            <div class="table_wrapper hod_table_wrapper kpi_goal_overview_height">
                <table class="table table-borderless table-space-between table-perfo-mngmt view_table">
                    <thead>
                      <tr class="fw-m table_head">
                        <td style="min-width:300px;">KPI</td>
                        <td style="min-width:150px;">Measurement</td>
                        <td style="min-width:400px;">Goal</td>
                        <td >Weight</td>
                        <td >Target {{ current_period }}</td>
                        <td >Actual {{ current_period }}</td>
                        <td >% Achieved(Actual / Target)</td>
                        <td >Weightage Score(%Achieved x Weightage%)</td>
                        <td >Remarks</td>
                      </tr>
                    </thead>
                    <tbody class="tbody-white">
                      {% if user_goals %}
                      {% regroup user_goals by kpiuser_goal.kpi_goal.kpi_category.name  as category_list %} 
                      {% for category in category_list %}
                      <tr>
                            <td scope="col" rowspan="{{ category.list|length|to_int|add:1 }}">{{ category.grouper }}</td>
                            {% for goal in category.list %}
                                  <tr>
                                    <td class="border_left_td">{{ goal.kpiuser_goal.kpi_goal.measurement_type.name }}</td>
                                    <td>{{ goal.kpiuser_goal.kpi_goal.name }} : <b>{{ goal.goal_value }}</b>; Max Finish Rate = <b>{{ goal.max_finish_rate }}</b>%</td>
                                    <td>{{ goal.weight }}%</td>
                                    {% get_percentage_flags goal as percentage_flags %}
                                    <td>{{ goal.target|format_with_percentage:percentage_flags.target_percentage }}</td>
                                    <td>{{ goal.actual|format_with_percentage:percentage_flags.actual_percentage }}</td>
                                    <td class="text-center">
                                      {% if goal.id %} 
                                      {% get_achieved_percentage goal.actual goal.target goal.max_finish_rate goal.kpiuser_goal.kpi_goal.measurement_type.calculation as achieved %}
                                          {{ achieved|floatformat:2 }}%
                                      {% else %}
                                        <div class="text-center">--</div>
                                      {% endif %}
                                    </td>
                                    <td class="text-center">
                                      {% if goal.id %}
                                      {% get_weightage_score goal.actual goal.target goal.weight goal.max_finish_rate goal.kpiuser_goal.kpi_goal.measurement_type.calculation as score %}
                                        <span class="score">{{ score|floatformat:2 }}</span>%
                                      {% else %}
                                        <div class="text-center">--</div>
                                      {% endif %}
                                    </td>
                                    <td>{% if goal.remark %} {{ goal.remark }} {% endif %}</td>

                                    <!-- <td>
                                      <a href="javascript:void(0);" data-fancybox="" data-src="#{{ goal.id }}">
                                          <img src="{% static 'admin/assets/images/doc_icon.svg' %}" alt="">
                                      </a>
                                      <div style="display:none;width:50%" class='p-5' id="{{ goal.id }}">
                                          <h3>Remarks</h3>
                                          Target comment
                                          <div style="width:100%" rows="10" class="table-box remarks">{% for goal_obj in user_goals_query_set %}{% if goal_obj.id == goal.id %}{% if goal_obj.user_target.first.remark %}{{ goal_obj.user_target.first.remark }}{% endif %}{% endif %}{% endfor %}</div><br>
                                          Actual comment
                                          <div style="width:100%" rows="10" class="table-box remarks">{% for goal_obj in user_goals_query_set %}{% if goal_obj.id == goal.id %}{% if goal_obj.user_actual.first.remark %}{{ goal_obj.user_actual.first.remark }}{% endif %}{% endif %}{% endfor %}</div>
                                          <div class="mt-5">
                                              <button type="submit" data-action="ok" data-id="{{ goal.id }}" class="gradient-button remark"><span class="btn-helper"></span>Close</button>
                                          </div>
                                        </div>
                                  </td> -->
                                  </tr>
                                {% endfor %}
            </tr> 
          {% endfor %}
          <tr class="green-bottom">
              <td colspan="7" style="text-align:right">Total</td>
              <td colspan="7" class="text-center">{{ user_goals.0.kpiuser_timeperiod.score }}%</td>
              <td></td>
              <td id="total_score"></td>
              <td></td>
          </tr>

          {% if appreciation_list %}
            <tr class="accordion-toggle green-bottom">
              <td colspan="7" style="text-align:right; cursor: pointer;" onclick="toggleAccordion(this)">
                Letter of praise
              </td>
              <td colspan="9" style="padding-left: 4%;" onclick="toggleAccordion(this)">
                {{ appreciation_value_sum }} %
              </td>
              <td onclick="toggleAccordion(this)"><span class="arrow up"></span></td>
            </tr>

            {% for appreciation_val in appreciation_list %}
              <tr class="accordion-appriciation-content" style="display: table-row;">
                <td colspan="7" style="text-align:right;">{{ appreciation_val.comments }} (Dated on: {{ appreciation_val.modification_date }})</td>
                <td colspan="9" style="padding-left: 4%;">{{ appreciation_val.value }}%</td>
                <td style="display: flex; flex-direction: row; width: 100px; align-items: center;">
                  <div style="margin-right: 10px;">
                      {% if appreciation_val.documents %}
                      <a href="{{ appreciation_val.documents.url }}" download="{{ appreciation_val.documents.name }}" data-file-name="{{ appreciation_val.documents }}" id="downloadButton" data-toggle="tooltip" class="imgButton" title="Download">
                          <img src="/static/admin/assets/images/kpi_goal_download.png" style="width: 20px; height: 20px;">
                      </a>
                      {% endif %}
                  </div>
  
              </td>
              </tr>
            {% endfor %}
          {% endif %}

          {% if criticism_list %}
            <tr class="accordion-toggle green-bottom">
              <td colspan="7" style="text-align:right; cursor: pointer;" onclick="toggleAccordionCriticism(this)">
                Letter of criticism
              </td>
              <td colspan="9" style="padding-left: 4%;" onclick="toggleAccordionCriticism(this)">
                {{ criticism_value_sum }} %
              </td>
              <td onclick="toggleAccordionCriticism(this)"><span class="arrow up"></span></td>

            </tr>

            {% for criticism_val in criticism_list %}
              <tr class="accordion-appriciation-content" style="display: table-row;">
                <td colspan="7" style="text-align:right; ">{{ criticism_val.comments }} (Dated on: {{ criticism_val.modification_date }})</td>
                <td colspan="9" style="padding-left: 4%; ">{{ criticism_val.value }}%</td>
                <td style="display: flex; flex-direction: row; width: 100px; align-items: center;">
                  <div style="margin-right: 10px;">
                      {% if criticism_val.documents %}
                      <a href="{{ criticism_val.documents.url }}" download="{{ criticism_val.documents.name }}" data-file-name="{{ criticism_val.documents }}" id="downloadButton" data-toggle="tooltip" class="imgButton" title="Download">
                          <img src="/static/admin/assets/images/kpi_goal_download.png" style="width: 20px; height: 20px;">
                      </a>
                      {% endif %}
                  </div>
  
              </td>
              </tr>
            {% endfor %}
          {% endif %}
          
          {% if user_goals.0.kpiuser_timeperiod.updated_score %}
            <tr class="green-bottom">
                <td colspan="7" style="text-align:right">Modified total</td>
                <td colspan="7" style="padding-left: 4%;" >{{ user_goals.0.kpiuser_timeperiod.updated_score }}%</td>
                <td></td>
                <td id="total_score"></td>
                <td></td>
            </tr>
          {% endif %}
        {% else %}
          <tr>
              <td colspan="9" style="text-align:center;">Approved goals not found for you on this year<td>
          </tr>
        {% endif %}
                    </tbody>
                  </table>
            </div>
          </div>



        <!-- {% if modified_log %}
          <div class="main-content-inner apply-leave-page">
            <div class="card-boxes apply-leave bg-white mt-20">
              <div class="table-top">
                <h4>Performance Modification</h4> 
              </div>
              
                  
              <ul class="form-box" style="padding: 20px 20px 20px 20px">
                <li>
                    <div class="input-box" style="width: 40%!important;">
                        <label for="revision_type">Score revision type</label>
                        <input type="text" name="revision_value" id="revision_value" value="{% if modified_log.value_type == 1 %} Letter of criticism {% else %} Letter of praise {% endif %}" readonly style="height: 40px;">
                    </div>
                    <div class="input-box" style="width: 40%!important;">
                        <label for="revision_value">Revision value (in %)</label>
                        <input type="text" name="revision_value" id="revision_value" value="{{ modified_log.value }}" readonly style="height: 40px;">
                    </div>
                  </li>
                  <li>
                    <div class="input-box" style="width: 40%!important;">
                        <label for="revision_value">Acheived total weightage</label>
                        <input type="text" name="revision_value" id="revision_value" value="{{ user_goals.0.kpiuser_timeperiod.score }} %" readonly style="height: 40px;">
                    </div>
                      <div class="input-box" style="width: 40%!important;">
                          <label for="revision_value">Modified total weightage</label>
                          <input type="text" name="revision_value" id="revision_value" value="{{ modified_log.updated_score }} %" readonly style="height: 40px;">
                      </div>
                  </li>
                  <li>
                      <div class="input-box" style="width: 100%!important;">
                          <label for="reason">Comments</label>
                          <textarea name="reason" id="reason" cols="30" rows="20" style="width:84%" readonly>{{ modified_log.comments }}</textarea>
                      </div>
                  </li>
              </ul>

              
            </div>
          </div>
        {% endif %} -->


          {% if user_goals %}
            <div class="card-boxes bg-white mt-20">
              <div class="table-top">
                <h4>KPI - Guidelines</h4> 
              </div>
              {% if guideline %}
                <div class="guide-lines mb-20">
                  {{guideline.content|safe}}
                </div>
              {% endif %}
              <div class="kpi-achivements">
                  <div class="achive-table-wrapper">
                    <table>
                      <thead>
                        <tr>
                          <th style="text-align: center;">KPI Achievement</th>
                          <th>Bonus Payout % </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td style="text-align: center;"><p class="increase">80% or above</p></td>
                          <td><p>Company KPI*30% + Personal KPI*70%</p></td>
                        </tr>
                        <tr>
                          <td style="text-align: center;"><p class="decrease">Less Than 80%</p></td>
                          <td><p>Management Decision</p></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="note">
                    <p class="text-red">Note:</p>
                    <p>Weightage Score = Achieved % x Weightage %</p>
                  </div>
              </div>
              
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block page_script %}
<script src="{% static 'admin/assets/js/kpi.js' %}?v={% now 'd_m_yH:i:s'%}"></script>
<script>
    {% if user_time_period.status == 3 or user_time_period.status == 4 or user_time_period.status == 5 %}
    var total = 0
                $("span.score").each(function(){
                    var value = parseFloat($(this).text())
                    total += value
                })
                $("#total_score").text(`${Number(total.toFixed(2))}%`)
    {% endif %}
</script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script>
  $(function(){
    if($("#kpi_approved").val() == 5)
    {
        $("#pdf_link").show()
    }
  })
</script>
{% endblock page_script %}
{% load my_filters %}

{% if user_goals %}
{% regroup user_goals by kpiuser_goal.kpi_goal.kpi_category.name  as category_list %} 
{% for category in category_list %}
    <tr>
        <td scope="col" rowspan="{{ category.list|length|to_int|add:1 }}">{{ category.grouper }}</td>
        {% for goal in category.list %}
            <tr>
                <td class="border_left_td">{{ goal.kpiuser_goal.kpi_goal.measurement_type.name }}</td>
                <td>{{ goal.kpiuser_goal.kpi_goal.name }} : <b>{{ goal.goal_value }}</b>; Max Finish Rate = <b>{{ goal.max_finish_rate }}</b>%</td>
                <td>{{ goal.weight }}%</td>
                {% get_percentage_flags goal as percentage_flags %}
                <td>{{ goal.target|format_with_percentage:percentage_flags.target_percentage }}</td>
                <td>{{ goal.actual|format_with_percentage:percentage_flags.actual_percentage }}</td>
                <td class="text-center">
                    {% if goal.id %}
                    {% get_achieved_percentage goal.actual goal.target goal.max_finish_rate goal.kpiuser_goal.kpi_goal.measurement_type.calculation as achieved %}
                    {{ achieved|floatformat:2 }}%
                    {% else %}
                    <div class="text-center">--</div>
                    {% endif %}
                </td>
                <td class="text-center">
                    {% if goal.id %}
                    {% get_weightage_score goal.actual goal.target goal.weight goal.max_finish_rate goal.kpiuser_goal.kpi_goal.measurement_type.calculation as score %}
                    <span class="score">{{ score|floatformat:2 }}</span>%
                    {% else %}
                    <div class="text-center">--</div>
                    {% endif %}
                </td>
                <td>{% if goal.remark %} {{ goal.remark }} {% endif %}</td>
            </tr> <!-- Removed duplicate <tr> -->
        {% endfor %}
    </tr> 
{% endfor %}
<tr class="green-bottom">
<td colspan="7" style="text-align:right">Total</td>
<td colspan="2" style="padding-left: 4%;" >{{ user_goals.0.kpiuser_timeperiod.score }} %</td>

<td id="total_score"></td>

</tr>

{% if appreciation_list %}
    <tr class="accordion-toggle green-bottom">
        <td colspan="7" style="text-align:right; cursor: pointer;" onclick="toggleAccordion(this)">
            Letter of praise
        </td>
        <td colspan="2" style="padding-left: 4%;" onclick="toggleAccordion(this)">
            {{ appreciation_value_sum }} %
        </td>
        <td onclick="toggleAccordion(this)"><span class="arrow up"></span></td>
        
    </tr>

    {% for appreciation_val in appreciation_list %}
        <tr class="accordion-appriciation-content" style="display: table-row;">
            <td colspan="7" style="text-align:right;">{{ appreciation_val.comments }} (Dated on: {{ appreciation_val.modification_date }})</td>
            <td colspan="2" style="padding-left: 4%;">{{ appreciation_val.value }} %</td>
            <td style="display: flex; flex-direction: row; width: 100px; align-items: center;">
                <div style="margin-right: 10px;">
                    {% if appreciation_val.documents %}
                        <a href="{{ appreciation_val.documents.url }}" download="{{ appreciation_val.documents.name }}" data-file-name="{{ appreciation_val.documents }}" id="downloadButton" data-toggle="tooltip" class="imgButton" title="Download">
                            <img src="/static/admin/assets/images/kpi_goal_download.png" style="width: 20px; height: 20px;">
                        </a>
                    {% endif %}
                </div>
                <div>
                    <button type="button" class="delete-log remove-button" data-id="{{ appreciation_val.id }}" style="width: 20px; height: 20px; padding: 0; border: none; background: transparent;">
                        <span class="icon-delete_icon" style="font-size: 16px;"></span>
                    </button>
                </div>
            </td>
        </tr>
    {% endfor %}
{% endif %}

{% if criticism_list %}
    <tr class="accordion-toggle green-bottom">
        <td colspan="7" style="text-align:right; cursor: pointer;" onclick="toggleAccordionCriticism(this)">
            Letter of criticism
        </td>
        <td colspan="2" style="padding-left: 4%;" onclick="toggleAccordionCriticism(this)">
            {{ criticism_value_sum }} %
        </td>
        <td  onclick="toggleAccordionCriticism(this)"><span class="arrow up"></span></td>
       
    </tr>

    {% for criticism_val in criticism_list %}
        <tr class="accordion-appriciation-content" style="display: table-row;">
            <td colspan="7" style="text-align:right;">{{ criticism_val.comments }} (Dated on: {{ criticism_val.modification_date }})</td> <!-- replaced placeholder '33' with a variable -->
            <td colspan="2" style="padding: unset; padding-left: 4%;">{{ criticism_val.value }} %</td>
            <td style="display: flex; flex-direction: row; width: 100px; align-items: center;">
                <div style="margin-right: 10px;">
                    {% if criticism_val.documents %}
                        <a href="{{ criticism_val.documents.url }}" download="{{ criticism_val.documents.name }}" data-file-name="{{ criticism_val.documents }}" id="downloadButton" data-toggle="tooltip" class="imgButton" title="Download">
                            <img src="/static/admin/assets/images/kpi_goal_download.png" style="width: 20px; height: 20px;">
                        </a>
                    {% endif %}
                </div>
                <div>
                    <button type="button" class="delete-log remove-button" data-id="{{ criticism_val.id }}" style="width: 20px; height: 20px; padding: 0; border: none; background: transparent;">
                        <span class="icon-delete_icon" style="font-size: 16px;"></span>
                    </button>
                </div>
            </td>
            
        </tr>
    {% endfor %}
{% endif %}

{% if user_goals.0.kpiuser_timeperiod.updated_score %}
<tr class="green-bottom">
    <td colspan="7" style="text-align:right">Modified total</td>
    <td colspan="2" style="padding-left: 4%;" >{{ user_goals.0.kpiuser_timeperiod.updated_score }} %</td>
  
    <td id="total_score"></td>

</tr>
{% endif %}
{% else %}
<tr>
    <td colspan="9" style="text-align:center;">Approved goals not found for you this year</td> <!-- fixed the missing closing </td> -->
</tr>
{% endif %}

{% extends "hisensehr/layout/user_base.html" %}
{% load static %}
{% load my_filters %}
{% block body_block %}

<style>
    .table-image-container
    {
        width:25px;
        height:25px;
    }
    .table-image-container img {
        width: 100%;
        height:100%;
    }
</style>
<div class="content">
    <input type="hidden" value="{{is_admin}}" id="is_admin_user">

    <div class="main-contents full-screen">
        <div class="page-top">
            <div>
            <h4>KPI Performance</h4>
            <p><a href="{% url 'appdashboard:dashboard' %}">Dashboard</a><span>/<span><a href="">KPI Management</a><span>/<span><a href="" class="current-page">KPI Performance</a><span></p>
            <!-- <p><a href="{% url 'appdashboard:dashboard' %}">Dashboard</a><span>/<span><a href="" class="current-page">KPI Performance</a><span></p> -->
            </div>
         </div> 
        <div class="main-content-inner kpi-approval-status">
            <input type="hidden" value="{% url 'appdashboard:emp_leave_detail' %}" id="load_leave_status">
            
            <div class="table-card-block mt-20 " style="overflow: unset;">
                <div class="table-top d-flex flex-wrap justify-content-between align-items-center p-20">
                    <h4>KPI Performance</h4>
                    <form action="" class="table-form">
                          <div class="search-box">
                            <input type="search" name="" class="table-search" placeholder="Search employee name, id .." id="emp_kpi_filter" onkeyup="filter_kpi_performance('')" value="{% if code != None %}{{code}}{% endif %}"> {% comment %}don't delete value{% endcomment %}
                            <img class="search-icon" src="{% static 'admin/assets/images/search_icon.svg' %}" alt="icon">
                        </div>
                        <div class="table-filter applied">
                            <div class="filter-box">
                              <p>More Filters</p>
                              <img src="{% static 'admin/assets/images/filter_icon.svg' %}"> 
                            </div>
                              <div class="filter-wrapper ">
                                <div class="filter-title"><h5>Sort By</h5>
                                    <span class="close-filter">×</span>
                                  </div>
                                <ul class="filter-list"> 
                                    <li class="filter-item">
                                        <select id="kpi_department" class="select-default" onchange="filter_kpi_performance('')">
                                            <option value="">All Departments</option>
                                            {% for department in departments %}
                                                <option value="{{ department.id }}">{{ department.name }}</option>
                                            {% endfor %}
                                        </select>
                                        
                                    </li>
                                    <li class="filter-item">
                                        <select id="kpi_designation" class="select-default" onchange="filter_kpi_performance('')">
                                            <option value="">All Designations</option>
                                            {% for designation in designations %}
                                                <option value="{{ designation.id }}">{{ designation.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </li>
                                    <li class="filter-item">
                                        <select id="kpi_year" class="select-default" id="filter_request_status" onchange="filter_get_quaters();">
                                            <option value="">Select year</option>
                                            {% for year in kpi_year %}
                                                <option value="{{ year.id }}" {% if year.current_year %}selected{% endif %}>{{ year.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </li>
                                    <li class="filter-item">
                                            <select id="kpi_quater" class="select-default" onchange="filter_kpi_performance('')">
                                                <option value="">All</option>
                                                {% for qtr_data in filter_quater %}
                                                    <option value="{{ qtr_data.id }}" {% if quater.id == qtr_data.id %}selected{% endif %}>{{ qtr_data.name }}</option>
                                                {% endfor %}
                                            </select>
                                    </li>
                                    <li class="filter-item">
                                        <select class="select-default" id="filter_performance_status" onchange="filter_kpi_performance('')">
                                            <option value="approved">Approved</option>
                                            <option value="pending">Pending</option>
                                            <option value="inprogress">Inprogress</option>
                                            <!-- <option value="approved">Approved</option> -->
                                            <option value="rejected">Rejected</option>
                                        </select>               
                                    </li>
                                </ul>
                              </div>
                          </div>
                                 
                    </form>
                </div>
            </div>

            <div id="kpi_performance_table" class="kpi_performance_table">
                <input type='hidden' id='previous_year' value='{{ quater.kpi_year.id }}'>
                
                {% if filter_quater|length > 1 and not kpi_quater_filter %}
                    <!-- Multiple time periods selected - show each in a scrollable section -->
                    {% for qtr in filter_quater %}
                        <div class="table-card-block mt-20">
                            <div class="table-top d-flex flex-wrap justify-content-between align-items-center p-20">
                                <h4>{{qtr.kpi_year.name}} {{ qtr.name }}</h4>
                                <div class="d-flex">
                                    <div class="top-action">     
                                        <a href="javascript:void(0)" onclick="generateKpiPerformancePdf({{qtr.id}})" data-toggle="tooltip" title="Download PDF">
                                            <img src="{% static 'admin/assets/images/downl_icon.svg' %}">
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="table_wrapper full_table_wrapper" style="max-height: 50vh; overflow-y: auto;">
                                <table class="table table-borderless table-space-between">
                                    <thead>
                                        <tr class="fw-m table_head">
                                            <td>Employee Name</td>
                                            <td>Employee ID</td>
                                            <td>Department</td>
                                            <td>Designation</td>
                                            <td>Approved by / Rejected by</td>
                                            <td>Score</td>
                                            <td>Status</td>
                                            <td style="text-align:center;"></td>
                                        </tr>
                                    </thead>
                                    <tbody class="tbody-white leave-requests-tbody">
                                        {% if distinct_users_list %}
                                            {% for user in distinct_users_list %}
                                                {% if user.kpiuser_timeperiod.kpi_time_period.id == qtr.id %}
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <a href="/performance-detail-view/{{ user.approved_rejected_user.id|encrypt_value }}/{{ user.kpiuser_timeperiod.kpi_time_period.id|encrypt_value }}/{{ 0|encrypt_value }}">
                                                                {% if user.approved_rejected_user.profile.profile_pic %}
                                                                    <img src="{{user.approved_rejected_user.profile.profile_pic.url}}" style="background-image:url({{user.approved_rejected_user.profile.profile_pic.url}})" class=" table-radius-round" alt="icon">
                                                                {% else %}
                                                                    <img src="{% static '/admin/assets/images/user.png' %}" style="background-image:url({% static '/admin/assets/images/user.png' %})" class=" table-radius-round" alt="icon">
                                                                {% endif %}
                                                                {{user.approved_rejected_user.first_name}} {{user.approved_rejected_user.last_name}}
                                                            </div>
                                                        </td>
                                                        <td>{{user.approved_rejected_user.profile.employee_id}}</td>
                                                        <td>{{user.approved_rejected_user.profile.department.name}}</td>
                                                        <td>{{user.approved_rejected_user.profile.designation.name}}</td>
                                                        <td class="approved" style="text-align: center;">
                                                            <ul class="d-flex" style="justify-content: left; gap: 6px; display: flex; flex-wrap: wrap;">
                                                                {% if kpi_approval_statuses_list %}
                                                                    {% for assignee in kpi_approval_statuses_list %}  
                                                                        {% if assignee.kpiuser_timeperiod.kpi_time_period.id == qtr.id and user.approved_rejected_user == assignee.approved_rejected_user %}
                                                                            {% if assignee.added_by.profile and assignee.added_by.profile.profile_pic %}
                                                                                <li class="{{assignee.approval_status|getclass}}" data-toggle="tooltip" title="{{assignee.added_by.first_name}} {{assignee.added_by.last_name}}">
                                                                                    <div class="table-image-container">
                                                                                        <img src="{{assignee.added_by.profile.profile_pic.url}}" style="background-image:url({{assignee.added_by.profile.profile_pic.url}});" alt="icon">
                                                                                    </div>
                                                                                </li>
                                                                            {% else %}
                                                                                <li class="{{assignee.approval_status|getclass}}" data-toggle="tooltip" title="{{assignee.added_by.first_name}} {{assignee.added_by.last_name}}">
                                                                                    <div class="table-image-container">
                                                                                        <img src="{% static '/admin/assets/images/user.png' %}" style="background-image:url({% static '/admin/assets/images/user.png' %});" alt="icon">
                                                                                    </div>
                                                                                </li>
                                                                            {% endif %}
                                                                        {% endif %}
                                                                    {% endfor %}
                                                                {% else %}
                                                                    ---
                                                                {% endif %}
                                                            </ul>
                                                        </td>
                                                        {% if user.kpiuser_timeperiod.updated_score is not None %}
                                                            <td>{{ user.kpiuser_timeperiod.updated_score }}</td>
                                                        {% else %}
                                                            <td>{{ user.kpiuser_timeperiod.score|default:"--" }}</td>
                                                        {% endif %}
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                {% if user.kpiuser_timeperiod.kpi_approval_status %}
                                                                    {% if user.kpiuser_timeperiod.kpi_approval_status == 'approved' %}
                                                                        <span class="table-bg-green">Approved</span>
                                                                    {% elif user.kpiuser_timeperiod.kpi_approval_status == 'pending' %}
                                                                        <span class="table-bg-yellow">Pending</span>
                                                                    {% elif user.kpiuser_timeperiod.kpi_approval_status == 'rejected' %}
                                                                        <span class="table-bg-red">Rejected</span>
                                                                    {% elif user.kpiuser_timeperiod.kpi_approval_status == 'inprogress' %}
                                                                        <span class="table-bg-violet">In progress</span>
                                                                    {% else %}
                                                                        <span>--</span>
                                                                    {% endif %}
                                                                {% endif %}
                                                            </div>
                                                        </td>
                                                        <td class="width-18">
                                                            <span class="drop-parent">
                                                                <img src="{% static '/admin/assets/images/drop_list.png' %}" alt="icon">
                                                                <ul class="drop-list">
                                                                    <li class="">
                                                                        {% if user.kpiuser_timeperiod.kpi_approval_status == 'approved' %}
                                                                            <a href="/performance-detail-view/{{ user.approved_rejected_user.id|encrypt_value }}/{{ user.kpiuser_timeperiod.kpi_time_period.id|encrypt_value }}/{{ 1|encrypt_value }}" id="view-approve-detail">Praise/Criticism</a>
                                                                        {% endif %}
                                                                            <a href="/performance-detail-view/{{ user.approved_rejected_user.id|encrypt_value }}/{{ user.kpiuser_timeperiod.kpi_time_period.id|encrypt_value }}/{{ 0|encrypt_value }}" id="view-approve-detail">View</a>
                                                                    </li>
                                                                </ul>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                {% endif %}
                                            {% endfor %}
                                        {% else %}
                                            <tr style="border-bottom: none"><td colspan="10"><li class="no-job-found" style="width: 100%; text-align: center; min-height: unset;">No Data Found</li></td></tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <!-- Single time period selected - show as before -->
                    <div class="table-card-block mt-20 ">
                        <div class="table-top d-flex flex-wrap justify-content-between align-items-center p-20">
                            <h4>{{quater.kpi_year.name}} {{ quater.name }}</h4>
                            <div class="d-flex">
                                <div class="top-action">     
                                    <a href="javascript:void(0)" onclick="generateKpiPerformancePdf({{quater.id}})" data-toggle="tooltip" title="Download PDF">
                                        <img src="{% static 'admin/assets/images/downl_icon.svg' %}">
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="table_wrapper full_table_wrapper" style="min-height: 50vh;">
                            <table class="table table-borderless table-space-between">
                                <thead>
                                <tr class="fw-m table_head">
                                    <td>Employee Name</td>
                                    <td>Employee ID</td>
                                    <td>Department</td>
                                    <td>Designation</td>
                                    <td>Approved by / Rejected by</td>
                                    <td>Score</td>
                                    <td>Status</td>
                                    <td style="text-align:center;"></td>
                                </tr>
                                </thead>
                                <tbody class="tbody-white leave-requests-tbody" id="kpi_performance_table_body">
                                    {% if distinct_users_list %}
                                        {% for user in distinct_users_list %}
                                            <tr >
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <a href="/performance-detail-view/{{ user.approved_rejected_user.id|encrypt_value }}/{{ user.kpiuser_timeperiod.kpi_time_period.id|encrypt_value }}/{{ 0|encrypt_value }}">
                                                        {% if user.approved_rejected_user.profile.profile_pic %}
                                                            <img src="{{user.approved_rejected_user.profile.profile_pic.url}}" style="background-image:url({{user.approved_rejected_user.profile.profile_pic.url}})" class=" table-radius-round" alt="icon">
                                                        {% else %}
                                                            <img src="{% static '/admin/assets/images/user.png' %}" style="background-image:url({% static '/admin/assets/images/user.png' %})" class=" table-radius-round" alt="icon">
                                                        {% endif %}
                                                        {{user.approved_rejected_user.first_name}} {{user.approved_rejected_user.last_name}}
                                                    </div>
                                                </td>
                                                <td>{{user.approved_rejected_user.profile.employee_id}}</td>
                                                <td>{{user.approved_rejected_user.profile.department.name}}</td>
                                                <td>{{user.approved_rejected_user.profile.designation.name}}</td>
                                                <td class="approved"  style="text-align: center;">
                                                    <ul class="d-flex" style="justify-content: left;">
                                                        {% if kpi_approval_statuses_list %}
                                                            {% for assignee in kpi_approval_statuses_list %}  
                                                                {% if assignee.kpiuser_timeperiod.kpi_time_period.id  == quater.id and user.approved_rejected_user == assignee.approved_rejected_user %}
                                                                    {% if assignee.added_by.profile and assignee.added_by.profile.profile_pic %}
                                                                    <li class="{{assignee.approval_status|getclass}}" data-toggle="tooltip" title="{{assignee.added_by.first_name}} {{assignee.added_by.last_name}}">
                                                                            <div class="table-image-container">
                                                                                <img src="{{assignee.added_by.profile.profile_pic.url}}" style="background-image:url({{assignee.added_by.profile.profile_pic.url}});"   alt="icon">
                                                                            </div>
                                                                        </li>
                                                                    {% else %}
                                                                    <li class="{{assignee.approval_status|getclass}}" data-toggle="tooltip" title="{{assignee.added_by.first_name}} {{assignee.added_by.last_name}}">
                                                                        <div class="table-image-container">
                                                                            <img src="{% static '/admin/assets/images/user.png' %}" style="background-image:url({% static '/admin/assets/images/user.png' %});" alt="icon">
                                                                        </div>
                                                                        </li>
                                                                    {% endif %}
                                                                    
                                                                {% endif %}
                                                            
                                                            {% endfor %}
                                                        {% else %}
                                                            ---
                                                        {% endif %}
                                                    
                                                    </ul>
                                                </td>
                                                {% if user.kpiuser_timeperiod.updated_score is not None %}
                                                    <td>{{ user.kpiuser_timeperiod.updated_score }}</td>
                                                {% else %}
                                                    <td>{{ user.kpiuser_timeperiod.score|default:"--" }}</td>
                                                {% endif %}
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                            {% if user.kpiuser_timeperiod.kpi_approval_status %}
                                                                {% if user.kpiuser_timeperiod.kpi_approval_status == 'approved' %}
                                                                    <span class="table-bg-green">Approved</span>
                                                                {% elif user.kpiuser_timeperiod.kpi_approval_status == 'pending' %}
                                                                    <span class="table-bg-yellow">Pending</span>
                                                                {% elif user.kpiuser_timeperiod.kpi_approval_status == 'rejected' %}
                                                                    <span class="table-bg-red">Rejected</span>
                                                                {% elif user.kpiuser_timeperiod.kpi_approval_status == 'inprogress' %}
                                                                    <span class="table-bg-violet">In progress</span>
                                                                {% else %}
                                                                    <span>--</span>
                                                                {% endif %}
                                                            {% endif %}
                                                                    
                                                    </div>
                                                </td>
                                                <td class="width-18">
                                                    <span class="drop-parent">
                                                        <img src="{% static '/admin/assets/images/drop_list.png' %}" alt="icon">
                                                        <ul class="drop-list">
                                                            <li class="">
                                                                <!-- <a href="javascript:void(0);" id="view-approve-detail" onclick="loadPerformanceDetailView('{{user.kpiuser_timeperiod.kpi_time_period.id}}', '{{user.approved_rejected_user.id}}')">View</a> -->
                                                                {% if user.kpiuser_timeperiod.kpi_approval_status == 'approved' %}
                                                                    <a href="/performance-detail-view/{{ user.approved_rejected_user.id|encrypt_value }}/{{ user.kpiuser_timeperiod.kpi_time_period.id|encrypt_value }}/{{ 1|encrypt_value }}" id="view-approve-detail">Praise/Criticism</a>
                                                                {% endif %}
                                                                    <a href="/performance-detail-view/{{ user.approved_rejected_user.id|encrypt_value }}/{{ user.kpiuser_timeperiod.kpi_time_period.id|encrypt_value }}/{{ 0|encrypt_value }}" id="view-approve-detail">View</a>
                                                            </li>
                                                        </ul>
                                                    </span>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr style="border-bottom: none"><td colspan="10"><li class="no-job-found" style="width: 100%; text-align: center; min-height: unset;">No Data Found</li></td></tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                {% endif %}
            </div>
            <!-- <div class="pagination-outer align-items-center justify-content-between d-flex mt-20" id="kpi-performance-pagination-div">
                {% include 'hisensehr/kpi/performance/kpi_performance_pagination.html' %}
            </div> -->
        
    </div>
</div>

  
<style>
    .table td {
       width:13%!important;
    }
</style>

{% endblock %}
{% block page_script %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js" integrity="sha512-rstIgDs0xPgmG6RX1Aba4KV5cWJbAMcvRCVmglpam9SoHZiUCyQVDdH2LPlxoHtrv17XWblE/V/PP+Tr04hbtA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="{% static 'admin/assets/js/kpi.js' %}?v={% now 'd_m_yH:i:s'%}"></script>

{% endblock page_script %}


{% extends "hisensehr/layout/user_base.html" %}
{% load static %}
{% load my_filters %}
{% block body_block %}
<style>
  .error{
    color:red;
    margin-top:5px;
  }

  /* .accordion-toggle {
        font-weight: bold;
        background-color: #e0e0e0;
        text-align: left;
        cursor: pointer;
    } */

    /* .accordion-appriciation-content {
        background-color: #f9f9f9;
    } */

    .arrow {
      display: inline-block;
      width: 0;
      height: 0;
      /* margin-left: 10px; */
      vertical-align: middle;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 10px solid white;
      cursor: pointer;
      transition: transform 0.3s ease;
    }

    .arrow.up {
      transform: rotate(180deg);
    }
</style>
<input type="hidden" id="current_period_id" value="{{ current_period_id }}">

<div class="content">
    <div class="main-contents full-screen">
        <div class="page-top">
            <div> 
              <h4>KPI Performance</h4>
              <p><a href="{% url 'appdashboard:dashboard'%}">Dashboard</a><span>/<span><a href="" class="current-page">KPI Performance</a><span></p>
        </div>
    </div> 
    <div class="main-content-inner">
      <div class="card-boxes bg-white mt-20">
        <div class="d-flex profile-basic-info kpi-top">
          <div class="profile-info-item align-items-center">
            <span>Full Name</span>
            <p>{{ current_user.approved_rejected_user.first_name }} {{ current_user.approved_rejected_user.last_name }}</p>
          </div>
          <div class="profile-info-item">
            <span>Department</span>
            <p>{{ current_user.approved_rejected_user.profile.department.name }}</p>
          </div>
          <div class="profile-info-item" >
            <span>Title</span>
            <p>{{ current_user.approved_rejected_user.profile.designation.name }}</p>
          </div>
          <div class="profile-info-item" >
            <span>KPI Year - Time period</span>
            <p>{{ year_name  }} - {{ current_quater_name }}</p>
          </div>
        </div>
   
        <div class="navigation_menu" id="navigation_menu">
            <ul class="navigation_tabs" id="navigation_tabs">
            {% if kpi_apprv_list %}
              {% for assignee in kpi_apprv_list %}  
                  {% if assignee.kpilevel_id_id == 2 %} 
                      <li class="{% if assignee.is_approve %}tab_inactive{% else %}tab_disabled{% endif %}">
                          <span class="top_status">{% if assignee.is_approve %}Created & Approved{% else %}Created{% endif %}</span>
                          <a href="#">{{assignee.added_by.first_name}} {{assignee.added_by.last_name}}</a>
                          <p class="status_date">{{assignee.updated_at|time_uae_format}}</p>
                      </li>
                  {% else %}
                      <li class="{% if assignee.is_approve and assignee.approval_status == 'approved' %}tab_inactive{% elif not assignee.is_approve and assignee.approval_status == 'pending' %}tab_disabled{% elif not assignee.is_approve and assignee.approval_status == 'rejected' %}tab_rejected{% endif %}">
                          <span class="top_status">{{assignee.approval_status|getCapitialized}}</span>
                          <a href="#">{{assignee.added_by.first_name}} {{assignee.added_by.last_name}}</a>
                          <p class="status_date">{{assignee.updated_at|time_uae_format}}</p>
                      </li>
                  {% endif %}
              {% endfor %}
            {% endif %}
            
            </ul>
        </div>
    </div>

        
        <div class="table-card-block mt-20 ">
          <div class="table-top d-flex justify-content-between align-items-center p-20">
              <h4>KPI - Overview</h4>
              <div class="d-flex">
                <div class="top-action" id="pdf_link" style="display:flex">     
                  <a href="{% url 'appdashboard:my_kpi_pdf' current_period_id current_user.approved_rejected_user.id %}"  data-toggle="tooltip" title="Download Sheet">
                    <img src="{% static 'admin/assets/images/downl_icon.svg' %}">
                  </a>
                </div>
                <div class="top-action">
                  <a href="{% url 'appdashboard:kpi_performance'%}"  data-toggle="tooltip" title="KPI Performance">
                    <img src="{% static 'admin/assets/images/back-button.svg' %}">
                  </a>   
                </div>            
              </div>
          </div>
          <div class="table_wrapper hod_table_wrapper kpi_goal_overview_height">
              <table class="table table-borderless table-space-between table-perfo-mngmt view_table">
                  <thead>
                    <tr class="fw-m table_head">
                      <td style="min-width:300px;">KPI</td>
                      <td style="min-width:150px;">Measurement</td>
                      <td style="min-width:400px;">Goal</td>
                      <td >Weight</td>
                      <td >Target {{ current_period }}</td>
                      <td >Actual {{ current_period }}</td>
                      <td >% Achieved(Actual / Target)</td>
                      <td >Weightage Score(%Achieved x Weightage%)</td>
                      <td >Remarks</td>
                    </tr>
                  </thead>
                  <tbody class="tbody-white" id="performance_loaddata">
                    {% include 'hisensehr/kpi/performance/performance_detail_ajax.html' %}   
                  </tbody>
                </table>
          </div>
        </div>


      {% if user_goals %}
        <div class="main-content-inner apply-leave-page">
          {% if isedit == '1' %}
            <div class="card-boxes apply-leave bg-white mt-20">
              <div class="table-top">
                <h4>Performance Modification</h4> 
              </div>
                  <form method="post" action="{% url 'appdashboard:performance_modification' %}" name="performance_modification" id="performance_modification" enctype="multipart/form-data" autocomplete="off" style="padding:30px 30px 0px;">
                    {% csrf_token %}
                    <ul class="form-box">
                      <li>
                        <div class="input-box revision-input-wrapper">
                          <label for="revision_type">Score revision type<span>*</span></label>
                          <select class="select-leave" name="revision_type" id="revision_type"  >
                            <option value="">Select revision type</option>
                            <option value="1" >Letter of criticism</option>
                            <option value="2" >Letter of praise</option>
                          </select>
                          <span class="error" id="error_revision_type"></span>
                        </div>
                          <div class="input-box revision-input-wrapper">
                            <!-- <div style="display: flex; flex-direction: column;width: 76%;"> -->
                              <label for="revision_value">Revision value (in %)<span>*</span></label>
                              <input type="number" name="revision_value" id="revision_value"  min="0" step="0.01" max="100">
                              <span class="error" id="error_revision_value"></span>
                            <!-- </div> -->

                            <!-- <div style="display: flex; flex-direction: column;width: 20%;margin-left: 20px;width: 30%;">
                              <label for="updated_val">Updated Value</label>
                              <input type="number" name="updated_val"  id="updated_val" readonly style="height: 40px;background-color: #ededed;" min="0">
                            </div> -->
                          </div>
                        
                      </li>
                      <li>
                          <div class="input-box form-feild-box received-input-wrapper">
                              <label for="performance_date">Received Date<span>*</span></label>
                              <input type="text" class="date_picker perf_date date-selector" placeholder="Choose date" name="modification_date" id="modification_date">
                              <span class="error" id="error_modification_date"></span>
                          </div>
                      </li>
                      <li class="align-items-start">
                         <div class="input-box revision-input-wrapper"  id="choose-letter-div">
                            <!-- <div style="display: flex; flex-direction: column; width: 100%;"> -->
                              <label for="">Attach Certificate <span>*</span></label>
                              <div class="fileup-btn-type">
                                  <div class="fileup-upload-wrap">
                                      <input class="file-up-input" type='file' onchange="readURL(this);" data-add-target="1" id="performance-file-upload" accept=".pdf, .jpeg, .jpg, .png, .doc"/>
                                      <div class="drag-text">
                                          <a class="fileup-btn" type="button"
                                              onclick="$('.file-up-input').trigger( 'click' )">Choose File</a></h3>
                                      </div>
                                  </div>
                              <!-- </div> -->
                            </div>
                            <span class="error" id="error_document"></span>
                          </div>

                          <div class="input-box justify-content-end mb-0 revision-input-wrapper" style=" margin-top: 22px;" id="performance-letter-file-div">
                            <div class="file-up-wrapper d-flex align-items-center w-100" >

                                <div class="training-file-content me-0" id="addNewFile-1">
                                    <div class="training-file-wrap">
                                        <div class="image-details">
                                            <p class="image-name"></p>
                                            <p>100%</p>
                                        </div>
                                        <button type="button" onclick="removeFileUpload(this)" class="remove-file"
                                            data-remove-target="1"><span class="icon-delete_icon"></span></button>
                                    </div>
                                </div>
                            </div>
                          </div>
                      </li>
                      <li>
                        <div class="input-box received-input-wrapper">
                          <label for="reason">Comments<span>*</span></label>
                          <textarea name="reason" id="reason" cols="30" rows="20"></textarea>
                          <span class="error" id="error_reason_value"></span>
                        </div>
                      </li>
                    </ul>
                    <input type="hidden" name="updated_score" id="updated_score" value="{% if user_goals.0.kpiuser_timeperiod.updated_score %} {{ user_goals.0.kpiuser_timeperiod.updated_score }} {% else %} {{ user_goals.0.kpiuser_timeperiod.score }} {% endif %}">
                    <input type="hidden" name="timeperiod" id="timeperiod" value="{{ user_goals.0.kpiuser_timeperiod.id }}">
                    <input type="hidden" name="previous_score" id="previous_score" value="{{ user_goals.0.kpiuser_timeperiod.score }}">

                    <div class="d-flex button-block justify-content-end mt-20">
                      <button type="submit" id="mod__performance_submit" class="gradient-button performance_modification"><span class="btn-helper"></span>Submit</button>
                    </div>
                  </form>
                
              </div>
            </div>            
          {% endif %} 
        {% endif %}
        
            
            
        {% if user_goals %}
          <div class="card-boxes bg-white mt-20">
              <div class="table-top">
                <h4>KPI - Guidelines</h4> 
              </div>
              {% if guideline %}
                <div class="guide-lines mb-20">
                  {{guideline.content|safe}}
                </div>
              {% endif %}
              <div class="kpi-achivements">
                  <div class="achive-table-wrapper">
                    <table>
                      <thead>
                        <tr>
                          <th style="text-align: center;">KPI Achievement</th>
                          <th>Bonus Payout % </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td style="text-align: center;"><p class="increase">80% or above</p></td>
                          <td><p>Company KPI*30% + Personal KPI*70%</p></td>
                        </tr>
                        <tr>
                          <td style="text-align: center;"><p class="decrease">Less Than 80%</p></td>
                          <td><p>Management Decision</p></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="note">
                    <p class="text-red">Note:</p>
                    <p>Weightage Score = Achieved % x Weightage %</p>
                  </div>
              </div>
            </div>

            <div class="card-boxes accordian-boxes bg-white mt-20 p-0">
              <div class="table-top accordion-header d-flex justify-content-between p-20">
                <h4>Comments</h4> 
                <div class="accordion-btn"><span class="icon-Vector-2"></span></div>
              </div>
              <div class="accordian-content p-20">
              <div class="comments">
                <ul class="comments-info">
                {% if kpi_remarks_list %}
                  {% for comments in kpi_remarks_list %}  
                    {% if comments.remarks %}
                      <li class="comments-info-item">
                          <div class="flex-grow-1">
                            <span>{{ comments.remarks }}</span>
                              <div class="comments_info">
                                <p style="margin-right: 5px;">{{comments.kpi_added_by.first_name}} {{comments.kpi_added_by.last_name}}</p>
                                <p>{{comments.updated_at|time_uae_format}}</p>
                              </div>
                          </div>
                          <div class="d-flex align-items-center">
                            {% if comments.approval_status == 'approved' %}
                              <span class="table-bg-green">{{comments.approval_status}}</span>
                            {% else %}
                              <span class="table-bg-red">{{comments.approval_status}}</span>
                            {% endif %}

                          </div>
                          
                      </li>
                  {% endif %}
                  {% endfor %}
                {% else %}
                <p style="text-align: center;font-size: 14px;color:#979797">No comments</p> 
                {% endif %}
                </ul>
                 </div>
                </div>
          </div>

          </div>

           
          </div>
     
          {% endif %}
      </div>
</div>


<div class="kpi_approve_popup" id="approve_kpi" style="display:none;width: 50%;">
  <div class="pop-body">
      <h5>Approval</h5>
      <form action="{% url 'appdashboard:kpi_approval' %}" class="insurance-policy-form" name="approve_form" id="approve_form" method="post">
        {% csrf_token %}
        <div>
            <div class="chat-bottom" style="border-top: unset!important;">
              <div class="form-field-box ">
                <label for="">Comments: </label>
                <textarea class="form-control policy_desc w-100 remarks" onfocus="placeCursorToEnd('approve')"  id="approve" name="comments" style="cursor: default;">
                </textarea> 
              </div>
              <!-- <button type="submit" class="gradient-button"><span class="btn-helper"></span>Send</button> -->
            </div>
            <div class="btnAprve">
              <button type="submit" class="gradient-button " id="approve_popup_btn" onclick="approve_reject('approve_popup_btn')"><span class="btn-helper"></span>Approve</button>
            </div>
        </div>
          <input type="hidden" value="{{ qtr_id }}" id="qtr_id" name="qtr_id">
          <input type="hidden" value="0" id="yearid" name="yearid">
          <input type="hidden" value="{{ current_user.approved_rejected_user.id }}" id="kpi_id" name="kpi_id">
          <input type="hidden" name="msg_url" id="msg_url" data-url="{% url 'appdashboard:approval_message' %}">
      </form>
  </div>
</div>

<!-- Popup Ends -->

<!-- Popup -reject -->
<div class="kpi_reject_popup" id="reject_kpi" style="display:none;width: 50%;">
  <div class="pop-body">
      <h5>Reject</h5>
      <form action="{% url 'appdashboard:kpi_reject' %}" class="insurance-policy-form" name="reject_form" id="reject_form" method="post">
          {% csrf_token %}
          <div>
            <div class="chat-bottom" style="border-top: unset!important;">
              <div class="form-field-box ">
                <label for="">Comments: </label>
                <textarea class="form-control policy_desc w-100 remarks" id="reject" onfocus="placeCursorToEnd('reject')"  name="comments" style="cursor: default;">
                </textarea> 
              </div>
              <!-- <button type="submit" class="gradient-button"><span class="btn-helper"></span>Send</button> -->
            </div>
            <div class="btnAprve">
              <button type="submit" class="gradient-button " id="reject_popup_btn" onclick="approve_reject('reject_popup_btn')" ><span class="btn-helper"></span>Reject</button>
            </div>
        </div>
          <input type="hidden" value="{{ qtr_id }}" id="qtr_id" name="qtr_id">
          <input type="hidden" value="{{ current_user.approved_rejected_user.id }}" id="kpi_id" name="kpi_id">
      </form>
  </div>
</div>
<a id="fancyBoxShow" data-fancybox href="javascript:void(0)" data-type="pdf"></a>

<style>
  .table-bg-green,.table-bg-red {
    text-transform: capitalize;
  }
</style>
{% endblock %}
{% block page_script %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js" integrity="sha512-rstIgDs0xPgmG6RX1Aba4KV5cWJbAMcvRCVmglpam9SoHZiUCyQVDdH2LPlxoHtrv17XWblE/V/PP+Tr04hbtA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="{% static 'admin/assets/js/kpi-approval-level.js' %}?v={% now 'd_m_yH:i:s'%}"></script>
<!-- <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> -->
<script>
  $("#modification_date").datepicker({
      maxDate: new Date(),
      dateFormat: 'yy-mm-dd',
      onSelect: function() {
        $('#error_modification_date').text('');
    }
  }).prop('readonly', true);

    
  function placeCursorToEnd(id) {
    var textarea = document.getElementById(id);
    var value = textarea.value;
    textarea.value = '';  
    textarea.value = value.trim();  
    textarea.setSelectionRange(textarea.value.length, textarea.value.length); 
  }
</script>
<script>
  function approve_reject(val){
    if (val == 'approve_popup_btn')
    {
      $('#'+val).text('Approving')
      $('#'+val).prop('disabled', true)
      $('#approve_form').submit()
    }
    else
    {
      $('#'+val).text('Rejecting')
      $('#'+val).prop('disabled', true)
      $('#reject_form').submit()
    }
  }

  $(document).ready(function() {
    $(".accordian-boxes:first-of-type").addClass("show");
    $(".accordian-boxes").on("click", ".accordion-header", function() {
        $(".accordian-content").slideUp().removeClass("flipInX");
        $(".accordion-btn").removeClass("close");
        if ($(this).next().is(":hidden")) {
            $(this).next().slideDown().addClass("flipInX");
            $(this).find(".accordion-btn").addClass("close");
        } else {
            $(this).next().slideUp();
            $(this).find(".accordion-btn").addClass("open");
        }
    });
    $(document).on('click', '.profile_cancel', function() {
        $('.accordian-content').slideUp();
        $('.accordion-btn').removeClass('close');
        $('.accordion-btn').addClass('open');
    });

    $('#revision_value').on('keydown', function(event) {
      const allowedKeys = ['Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Delete'];
      const isNumber = event.key >= '0' && event.key <= '9';
      const isDot = event.key === '.';
      const currentValue = $(this).val();
      const cursorPosition = this.selectionStart;

      // Prevent default action for non-allowed keys
      if (!isNumber && !isDot && !allowedKeys.includes(event.key)) {
        event.preventDefault();
      }

      // Ensure only one dot is allowed
      if (isDot && currentValue.includes('.')) {
        event.preventDefault();
      }
    });

    $('#revision_value').on('input', function() {
      const value = parseFloat($(this).val());
      if (!isNaN(value) && value > 100) {
        $(this).val(100);
      }
    });
    $('#revision_type').on('change', function() {
        $('#error_revision_type').text('');
        previous_score = parseFloat($('#previous_score').val());
        revision_value = parseFloat($('#revision_value').val());
        revision_type = $('#revision_type').val();
        
        if (!Number.isNaN(revision_value)) {
          if (revision_type == 1) {
            updated_score = previous_score - revision_value;

          } else if (revision_type == 2) {
              updated_score = previous_score + revision_value;
          }
          $('#updated_val').val(updated_score.toFixed(2))
        }
    });

    $('#revision_value').on('input keyup paste', function(event) {
        $('#error_revision_value').text('');

        previous_score = parseFloat($('#previous_score').val());
        revision_type = $('#revision_type').val();
        revision_value = $('#revision_value').val();

        if (revision_type == '') {
            $('#error_revision_type').text('This field is mandatory');
            return false;
        } else if (revision_type == 1) {
            updated_score = previous_score - parseFloat(revision_value);
        } else if (revision_type == 2) {
            updated_score = previous_score + parseFloat(revision_value);
        }

        $('#updated_val').val(updated_score.toFixed(2));
    });

    $('#reason').on('input keyup paste', function(event) {
      $('#error_reason_value').text(''); // Hide error when input is valid
    })

    $('#performance-file-upload').on('change', function() {
        $('#error_document').text(''); // Hide error when a file is selected
    });

    $('#performance_modification').on('submit', function(e) {
      $('#mod__performance_submit').text('Submitting...');
      e.preventDefault(); // Prevent the default form submission

      // Validate the form fields
      var revisionType = $('#revision_type').val();
      var revisionValue = $('#revision_value').val();
      var reason = $('#reason').val();
      var modification_date = $('#modification_date').val();
      var fileInput = $('#performance-file-upload');
      var file = fileInput[0].files[0]; // Get the uploaded file
      var fileUploadVal = fileInput.val(); // Get file path to validate
      var allowedExtensions = /(\.pdf|\.jpeg|\.jpg|\.png|\.doc)$/i; // Allowed file extensions
      
      var isValid = true;

      if (revisionType === "") {
        $('#error_revision_type').text('This field is mandatory')
        $('#mod__performance_submit').text('Submit');
        isValid = false;
      }

      if (revisionValue === "") {
        $('#error_revision_value').text('This field is mandatory')
        $('#mod__performance_submit').text('Submit');
        isValid = false;
      }

      if (reason === "") {
        $('#error_reason_value').text('This field is mandatory')
        $('#mod__performance_submit').text('Submit');
        isValid = false;
      }

      if (modification_date === "") {
        $('#error_modification_date').text('This field is mandatory')
        $('#mod__performance_submit').text('Submit');
        isValid = false;
      }
      if (fileUploadVal === "") {
        $('#error_document').text('This field is mandatory')
        $('#mod__performance_submit').text('Submit');
        isValid = false;
      }

      if (fileUploadVal === "") {
          $('#error_document').text('This field is mandatory');
          $('#mod__performance_submit').text('Submit');
          isValid = false;
      } else if (!allowedExtensions.exec(fileUploadVal)) {
          // Validate the file extension
          $('#error_document').text('Invalid file type. Please upload PDF, JPEG, JPG, PNG, or DOC files.');
          $('#mod__performance_submit').text('Submit');
          isValid = false;
      }


      if (isValid) {
        // calculation 
        previous_score = parseFloat($('#previous_score').val());
        revision_value = parseFloat($('#revision_value').val());
        revision_type = $('#revision_type').val();
        
        if (revision_type == 1) {
            updated_score = previous_score - revision_value;
        } else if (revision_type == 2) {
            updated_score = previous_score + revision_value;
        }

        // Check if updated_score is negative
        if (updated_score < 0) {
            // Show confirmation alert
            Swal.fire({
                title: 'Are you sure?',
                text: "The updated score is negative. Do you want to proceed?",
                icon: 'warning',
                showCancelButton: true,

                buttonsStyling: false, // Disable SweetAlert2 styling
                confirmButtonText: "Yes, proceed!",
                cancelButtonText: "Cancel",
                reverseButtons: true,
                customClass: {
                    confirmButton: 'gradient-button', // Add your custom class for the confirm button
                    cancelButton: 'white-button',     // Add your custom class for the cancel button
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // AJAX form submission
                    var formData = new FormData(this);

                    $.ajax({
                        url: $(this).attr('action'),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#mod__performance_submit').text('Submit');
                            if (response.success) {
                                $(".msg_desc").text(response.message);
                                $("#flash_message_success").attr("style", "display:block;");
                                $("#performance_loaddata").html(response.template)
                                setTimeout(function() {
                                    $("#flash_message_success").attr("style", "display:none;");
                                    // location.reload();
                                }, 3500);
                            } else {
                                $(".msg_desc").text(response.message);
                                $("#flash_message_error").attr("style", "display:block;");
                                setTimeout(function() {
                                    $("#flash_message_error").attr("style", "display:none;");
                                    // location.reload();
                                }, 3500);
                            }
                        }
                    });
                }
            });
        } else {
            var fileInput = $("#performance-file-upload")[0]; 
            var file = fileInput.files[0]; 
            // AJAX form submission
            var formData = new FormData(this);
            formData.append("file", file);
            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#mod__performance_submit').text('Submit');
                    if (response.success) {
                        $(".msg_desc").text(response.message);
                        $("#flash_message_success").attr("style", "display:block;");
                        setTimeout(function() {
                            $("#flash_message_success").attr("style", "display:none;");
                            // location.reload();
                        }, 3500);
                        $("#performance_loaddata").html(response.template)
                        $("#performance_modification")[0].reset();
                        // hide the selected document div
                        replace_html =
                            '<div class="file-up-wrapper d-flex align-items-center" style="width: 78%">\
                                <div class="training-file-content" id="addNewFile-1">\
                                    <div class="training-file-wrap">\
                                        <div class="image-details">\
                                            <p class="image-name"></p>\
                                            <p>100%</p>\
                                        </div>\
                                        <button type="button" onclick="removeFileUpload(this)" class="remove-file" data-remove-target="1"><span class="icon-delete_icon"></span></button>\
                                    </div>\
                                </div>\
                            </div>'
                        $("#performance-letter-file-div").html(replace_html);

                    } else {
                        $(".msg_desc").text(response.message);
                        $("#flash_message_error").attr("style", "display:block;");
                        setTimeout(function() {
                            $("#flash_message_error").attr("style", "display:none;");
                            // location.reload();
                        }, 3500);
                    }
                }
            });
        }
    }
    });
  });

</script>

{% if messages %}
  {% for message in messages %}
    <script>
      $(".msg_desc").text('{{ message }}')
      $("#flash_message_success").attr("style","display:block;")
      setTimeout(function(){
        $("#flash_message_success").attr("style","display:none;")
      },3000)

      </script>
  {% endfor %}
{% endif %}

{% endblock page_script %}